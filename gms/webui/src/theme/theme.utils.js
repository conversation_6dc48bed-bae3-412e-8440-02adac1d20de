import { themeKey, themes } from './theme.constants';
import { lightTheme } from './light-theme';
import { darkTheme } from './dark-theme';

export const toggleTheme = (theme) => {
  if (theme) {
    localStorage.setItem(themeKey, theme);
    window.location.reload();
  }
};

export const getTheme = () => {
  const savedTheme = localStorage.getItem(themeKey),
    colorPrefDark =
      window.matchMedia &&
      window.matchMedia('(prefers-color-scheme: dark)').matches,
    assignedTheme = () => {
      if (savedTheme) {
        return savedTheme;
      } else {
        const activeTheme = colorPrefDark ? themes.dark : themes.light;
        toggleTheme(activeTheme);
        return activeTheme;
      }
    };

  return assignedTheme();
};

export const getThemeObj = () =>
  getTheme() === themes.light ? lightTheme : darkTheme;
