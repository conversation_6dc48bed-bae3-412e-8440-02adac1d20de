import { globalTokens, componentTokens } from './common-theme';

const token = {
  ...globalTokens,
  colorTextBase: '#555555',
  colorBgBase: '#ffffff',
  colorText: '#555555',
  colorTextSecondary: '#707070',
  colorTextTertiary: '#757575',
  colorTextQuaternary: '#949494',
  colorTextDisabled: '#999999',
  colorBgDisabled: '#55555526',
  colorBgElevated: '#f7f7f7',
  colorBgLayout: '#ffffff',
  colorBgSpotlight: '#424242',
  colorBgContainer: '#fafafa',
  colorBorder: '#cccccc',
  colorBorderSecondary: '#e2e2e2',
  colorBlack: '#212121',
  colorWhite: '#ffffff',
  colorLink: '#0C66E4',
  colorLinkHover: '#0C60D6',
  colorLinkActive: '#0864E4',
  colorPrimary: '#0C66E4',
  colorPrimaryBg: '#D8E8FF',
  colorPrimaryBgHover: '#CCE1FF',
  colorPrimaryBorder: '#0C66E4',
  colorPrimaryBorderHover: '#085DD3',
  colorPrimaryHover: '#085DD3',
  colorPrimaryActive: '#2168CB',
  colorPrimaryTextHover: '#085DD3',
  colorPrimaryText: '#0C66E4',
  colorPrimaryTextActive: '#2168CB',
  colorSuccess: '#0daf0d',
  colorSuccessBg: '#f3fff3',
  colorSuccessBgHover: '#eaffea',
  colorSuccessBorder: '#0daf0d',
  colorSuccessBorderHover: '#0eac0e',
  colorSuccessHover: '#0dba0d',
  colorSuccessActive: '#0fbd0f',
  colorSuccessTextHover: '#0bc20b',
  colorSuccessText: '#0daf0d',
  colorSuccessTextActive: '#0cb60c',
  colorWarning: '#E6A80A',
  colorWarningBg: '#FFFBEB',
  colorWarningBgHover: '#FFF8DB',
  colorWarningBorder: '#E6A80A',
  colorWarningBorderHover: '#FCB80D',
  colorWarningHover: '#FCB80D',
  colorWarningActive: '#FCD80D',
  colorWarningTextHover: '#FCB80D',
  colorWarningText: '#E6A80A',
  colorWarningTextActive: '#FCD80D',
  colorError: '#ff0000',
  colorErrorBgTransparent: '#ff000020',
  colorErrorBg: '#FFF0F0',
  colorErrorBgHover: '#fff9f9',
  colorErrorBorder: '#ff0000',
  colorErrorBorderHover: '#ffa39e',
  colorErrorHover: '#ff7875',
  colorErrorActive: '#f20505',
  colorErrorTextHover: '#ec2b26',
  colorErrorText: '#ff4d4f',
  colorErrorTextActive: '#d9363e',
  colorInfo: '#6CABFF',
  colorInfoBg: '#F5F7FF',
  colorInfoBgHover: '#B7D4FF',
  colorInfoBorder: '#6cabff',
  colorInfoBorderHover: '#63a3fa',
  colorInfoHover: '#60A2FA',
  colorInfoActive: '#5FA3FF',
  colorInfoTextHover: '#589BF5',
  colorInfoTextActive: '#60a4ff',
  colorInfoText: '#3D6192',
  colorHighlight: '#CEE4FF',
  colorHighlightWeak: '#EFF8FF',
  colorHighlightXWeak: '#f5fbff',
  colorHighlightStrong: '#00B0F0',
  colorHighlightXStrong: '#3487E6',
  colorLan: '#B9D8FF',
  colorLanWeak: '#C2CBEF',
  colorWan: '#677ED6',
  colorWanWeak: '#8Ea4FF',
  colorWanPayload: '#00006B',
  colorNetRatio: '#177320',
  colorNetTotal: '#3311FF',
  colorNetCreated: '#FDBF6F',
  colorNetDeleted: '#CAB2D6',
  colorNetDenied: '#FFA500',
  colorNetDeniedWeak: '#FFDB99',
  colorAccent: '#DDDDDD',
  colorAccentWeak: '#E2E2E2',
  colorAccentXWeak: '#EFEFEF',
  colorAccentXXWeak: '#f9f9f9',
  colorAccentStrong: '#999999',
  colorAccentXStrong: '#555555',
  boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.12)',
  boxShadowSecondary: '0px 12px 24px rgba(0, 0, 0, 0.24)',
  alarmOk: '#0daf0d',
  alarmOkBg: '#f3fff3',
  alarmOkBgHover: '#eaffea',
  alarmOkBorder: '#0daf0d',
  alarmOkBorderHover: '#0eac0e',
  alarmOkHover: '#0dba0d',
  alarmOkActive: '#0fbd0f',
  alarmOkTextHover: '#0bc20b',
  alarmOkText: '#0daf0d',
  alarmOkTextActive: '#0cb60c',
  alarmUnknown: '#C4C2CE',
  alarmUnknownBg: '#FBFAFF',
  alarmUnknownBgHover: '#F6F4FF',
  alarmUnknownBorder: '#C4C2CE',
  alarmUnknownBorderHover: '#BEBBCB',
  alarmUnknownHover: '#C0BECC',
  alarmUnknownActive: '#BFBCCE',
  alarmUnknownTextHover: '#B8B5C8',
  alarmUnknownText: '#C4C2CE',
  alarmUnknownTextActive: '#BCB9CB',
  alarmCritical: '#FF0000',
  alarmCriticalBg: '#FFF5F5',
  alarmCriticalBgHover: '#FFEBEB',
  alarmCriticalBorder: '#FF0000',
  alarmCriticalBorderHover: '#FA0303',
  alarmCriticalHover: '#F60404',
  alarmCriticalActive: '#FF0B0B',
  alarmCriticalTextHover: '#F80A0A',
  alarmCriticalText: '#931A1A',
  alarmCriticalTextActive: '#FF0909',
  alarmMajor: '#FFA500',
  alarmMajorBg: '#FFF7EB',
  alarmMajorBgHover: '#FFF4DF',
  alarmMajorBorder: '#FFA500',
  alarmMajorBorderHover: '#FCA404',
  alarmMajorHover: '#F9A203',
  alarmMajorActive: '#FFA809',
  alarmMajorTextHover: '#FAA304',
  alarmMajorText: '#A36A01',
  alarmMajorTextActive: '#FFA80A',
  alarmMinor: '#FFFF00',
  alarmMinorBg: '#FFFFE7',
  alarmMinorBgHover: '#F9F9D0',
  alarmMinorBorder: '#FFFF04',
  alarmMinorBorderHover: '#FBFB05',
  alarmMinorHover: '#FAFA04',
  alarmMinorActive: '#FFFF0A',
  alarmMinorTextHover: '#F9F904',
  alarmMinorText: '#FBFB04',
  alarmMinorTextActive: '#FBFB18',
  alarmWarning: '#00FFFF',
  alarmWarningBg: '#FAFFFF',
  alarmWarningBgHover: '#F5FFFF',
  alarmWarningBorder: '#00FFFF',
  alarmWarningBorderHover: '#02EDED',
  alarmWarningHover: '#02EDED',
  alarmWarningActive: '#00FFFF',
  alarmWarningTextHover: '#04E5E5',
  alarmWarningText: '#016F6F',
  alarmWarningTextActive: '#02F2F2',
  colorProtocolTCP: '#8DDDD0',
  colorProtocolUDP: '#FFA056',
  colorProtocolICMP: '#6F4E7C',
  colorProtocolOther: '#CA472F',
  colorProtocolAll: '#0B84A5',
  colorClientLatency: '#FFC97F',
  colorServerLatency: '#D9DEF5',
  colorGradient:
    'linear-gradient(to bottom right, var(--ant-color-brand) 28%, rgba(12, 102, 228, 1) 38%, rgba(31, 19, 46, 1) 50%);',
  chartColors: [
    '#A6CEE3',
    '#1F78B4',
    '#B2DF8A',
    '#33A02C',
    '#045A8D',
    '#fdbf6f',
    '#cab2d6',
    '#6a3d9a',
    '#fb9a99',
    '#ff7f00',
    '#c3b23c',
    '#b15928',
    '#888888',
    '#d9d9d9',
    '#cccccc',
    '#fccde5',
    '#a85f57',
    '#D95F02',
    '#b34c9b',
    '#978968',
    '#629d9b',
    '#8db04f',
    '#9b8464',
    '#aaaaaa',
    '#ffff99',
    '#666666',
  ],
  chartProtocolColors: ['#8DDDD0', '#FFA056', '#6F4E7C', '#CA472F', '#0B84A5'],
  mapStyle: '17ed8d0cbf96ab67',
};

export const lightTheme = {
  cssVar: 'true',
  token,
  components: { ...componentTokens },
};
