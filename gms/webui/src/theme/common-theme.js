export const globalTokens = {
  colorBrand: '#01A982',
  size: 12,
  sizeMS: 14,
  sizeMD: 16,
  sizeLG: 18,
  sizeXL: 22,
  sizeXXL: 26,
  sizeStep: 4,
  sizeUnit: 4,
  fontFamily:
    "'MetricHPE', Arial, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",
  fontSizeXS: 10,
  fontSize: 12,
  fontSizeSM: 14,
  fontSizeMD: 16,
  fontSizeLG: 18,
  fontSizeXL: 22,
  paddingXXS: 2,
  paddingXS: 4,
  paddingSM: 8,
  padding: 12,
  paddingMD: 16,
  paddingLG: 18,
  paddingXL: 22,
  paddingXXL: 26,
  marginXXS: 2,
  marginXS: 4,
  marginSM: 8,
  margin: 12,
  marginMD: 16,
  marginLG: 18,
  marginXL: 22,
  marginXXL: 26,
  fontSizeHeading1: 16,
  fontSizeHeading2: 14,
  fontSizeHeading3: 13,
  fontSizeHeading4: 12,
  fontSizeHeading5: 12,
  borderRadius: 4,
  borderRadiusXS: 2,
  borderRadiusSM: 3,
  borderRadiusLG: 6,
  controlHeight: 24,
  controlHeightXS: 20,
  controlHeightSM: 22,
  controlHeightLG: 28,
  motionDurationFast: '0.1s',
  motionDurationMid: '0.175s',
  motionDurationSlow: '0.2s',
};

export const componentTokens = {
  Tree: {
    fontSize: 'var(--ant-font-size)',
    colorBgContainer: 'none',
    nodeHoverBg: 'var(--ant-color-highlight)',
    nodeSelectedBg: 'var(--ant-color-highlight-weak)',
    titleHeight: 'var(--ant-control-height)',
  },
  Button: {
    contentFontSize: 12,
    contentFontSizeLG: 14,
    contentFontSizeSM: 10,
    paddingInline: 10,
    paddingInlineLG: 12,
    defaultBg: 'var(--ant-color-bg-elevated)',
    defaultColor: 'var(--ant-color-text)',
    defaultBorderColor: 'var(--ant-color-border)',
    defaultHoverBorderColor: 'var(--ant-color-primary-border-hover)',
    defaultHoverColor: 'var(--ant-color-primary-hover)',
  },
  ChartLegend: {
    Line: {
      itemHeight: 2,
      itemWidth: 10,
    },
    Box: {
      itemHeight: 10,
      itemWidth: 10,
    },
  },
  Typography: {
    fontWeightStrong: 600,
  },
  Menu: {
    itemPaddingInline: 12,
    itemHeight: 24,
    itemBg: 'var(--ant-color-bg-layout)',
    itemHoverBg: 'var(--ant-color-highlight-weak)',
    darkItemBg: 'var(--ant-color-bg-layout)',
    groupTitleColor: 'var(--ant-menu-item-color)',
    motionDurationSlow: '0.1s',
  },
  Pagination: {
    itemSize: 24,
    itemSizeSM: 18,
  },
  Radio: {
    buttonCheckedBg: 'var(--ant-color-bg-layout)',
    colorTextDisabled: 'var(--ant-color-text-disabled)',
    colorBgContainer: 'var(--ant-color-bg-elevated)',
    wrapperMarginInlineEnd: 12,
    radioSize: 16,
  },
  Select: {
    multipleItemHeightLG: 26,
    optionActiveBg: 'var(--ant-color-accent-weak)',
    optionSelectedBg: 'var(--ant-color-highlight)',
  },
  Avatar: {
    containerSize: 24,
    containerSizeLG: 26,
    textFontSize: 12,
    textFontSizeLG: 14,
    textFontSizeSM: 10,
  },
  Carousel: {
    controlHeightLG: 26,
  },
  Collapse: {
    headerBg: 'transparent',
    contentBg: 'transparent',
    headerPadding: 'var(--ant-padding-xs) 0',
  },
  Descriptions: {
    titleMarginBottom: 8,
    itemPaddingBottom: 4,
    paddingSM: 4,
    padding: 8,
    paddingLG: 12,
  },
  List: {
    itemPaddingLG: '8px 16px',
    itemPaddingSM: '4px 8px',
    itemPadding: '12px 0',
    emptyTextPadding: 12,
  },
  Segmented: {
    controlPaddingHorizontal: 10,
    itemSelectedColor: 'var(--ant-color-primary)',
    itemSelectedBg: 'transparent',
  },
  Table: {
    cellPaddingInline: 10,
    cellPaddingInlineSM: 6,
    cellPaddingBlockSM: 4,
    headerBorderRadius: 4,
    cellPaddingBlock: 8,
    cellPaddingBlockMD: 6,
  },
  Tabs: {
    cardBg: 'var(--ant-color-accent-xx-weak)',
    itemHoverColor: 'var(--ant-color-primary-text-hover)',
    itemSelectedColor: 'var(--ant-color-primary-text-active)',
    cardHeight: 'var(--ant-control-height)',
    cardPadding: '2px 10px',
    cardPaddingLG: '4px 12px 8px',
    borderRadius: 2,
    borderRadiusLG: 4,
    motionDurationSlow: `0.1s`,
    cardGutter: 4,
    horizontalMargin: '0',
  },
  Tag: {
    paddingXXS: 4,
  },
  Tooltip: {
    paddingXS: 10,
    zIndexPopup: 1201,
  },
  Alert: {
    defaultPadding: '4px 8px',
    withDescriptionPadding: '8px 12px',
  },
  Drawer: {
    footerPaddingInline: 12,
  },
  Message: {
    contentPadding: '8px 12px',
  },
  Modal: {
    headerBg: 'var(--ant-color-bg-layout)',
    contentBg: 'var(--ant-color-bg-layout)',
    boxShadow: 'var(--ant-box-shadow-secondary)',
    contentPadding:
      'var(--ant-padding-sm) var(--ant-padding) var(--ant-padding) var(--ant-padding)',
  },
  Notification: {
    width: '50vw',
    FontSizeLG: 14,
    controlHeightLG: 'var(--ant-control-height)',
    paddingMD: 'var(--ant-padding)',
    paddingContentHorizontalLG: 'var(--ant-padding)',
  },
  Popconfirm: {
    marginXS: 12,
  },
  Popover: {
    colorBgElevated: 'var(--ant-color-bg-layout)',
  },
  Form: {
    itemMarginBottom: 8,
    labelColonMarginInlineEnd: 'var(--ant-margin-sm)',
  },
  Layout: {
    headerHeight: 60,
    headerPadding: '0 var(--ant-padding-lg)',
    headerBg: 'var(--ant-color-bg-layout)',
    siderBg: 'var(--ant-color-bg-layout)',
  },
  Transfer: {
    controlItemBgActive: 'var(--ant-color-highlight)',
    controlItemBgActiveHover: 'var(--ant-color-highlight)',
  },
  Switch: {
    trackHeight: 18,
    handleSize: 14,
  },
};
