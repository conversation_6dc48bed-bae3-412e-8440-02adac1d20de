import { theme } from 'antd';

import { globalTokens, componentTokens } from './common-theme';

const { darkAlgorithm } = theme;

const token = {
  ...globalTokens,
  colorText: '#FFFFFF',
  colorTextSecondary: '#DDDDDD',
  colorTextTertiary: '#CCCCCC',
  colorTextQuaternary: '#BABABA',
  colorTextDisabled: '#C3C3C3',
  colorBgDisabled: '#FFFFFF',
  colorBgElevated: '#363636',
  colorBgLayout: '#323232',
  colorBgSpotlight: '#222222',
  colorBgContainer: '#393939',
  colorBorder: '#6A6A6A',
  colorBorderSecondary: '#464545',
  colorBlack: '#212121',
  colorWhite: '#ffffff',
  colorLink: '#699FF7',
  colorLinkHover: '#7BADFF',
  colorLinkActive: '#83AFF6',
  colorPrimary: '#699FF7',
  colorPrimaryBg: '#30486F',
  colorPrimaryBgHover: '#3D5B8B',
  colorPrimaryBorder: '#699FF7',
  colorPrimaryBorderHover: '#83B2FF',
  colorPrimaryHover: '#83B2FF',
  colorPrimaryActive: '#84B2FD',
  colorPrimaryTextHover: '#83B2FF',
  colorPrimaryText: '#699FF7',
  colorPrimaryTextActive: '#84B2FD',
  colorSuccess: '#0daf0d',
  colorSuccessBg: '#013801',
  colorSuccessBgHover: '#014501',
  colorSuccessBorder: '#0daf0d',
  colorSuccessBorderHover: '#0eac0e',
  colorSuccessHover: '#0dba0d',
  colorSuccessActive: '#0fbd0f',
  colorSuccessTextHover: '#0bc20b',
  colorSuccessText: '#0daf0d',
  colorSuccessTextActive: '#0cb60c',
  colorWarning: '#E6A80A',
  colorWarningBg: '#976E07',
  colorWarningBgHover: '#A5790A',
  colorWarningBorder: '#E6A80A',
  colorWarningBorderHover: '#FCB80D',
  colorWarningHover: '#FCB80D',
  colorWarningActive: '#FCD80D',
  colorWarningTextHover: '#FCB80D',
  colorWarningText: '#E6A80A',
  colorWarningTextActive: '#03DCDC',
  colorError: '#D25656',
  colorErrorBg: '#400000',
  colorErrorBgHover: '#590101',
  colorErrorBorder: '#D25656',
  colorErrorBorderHover: '#E15B5B',
  colorErrorHover: '#E25959',
  colorErrorActive: '#E05C5C',
  colorErrorTextHover: '#E55A5A',
  colorErrorText: '#FFB4B4',
  colorErrorTextActive: '#E35C5C',
  colorInfo: '#6CABFF',
  colorInfoBg: '#142134',
  colorInfoBgHover: '#203551',
  colorInfoBorder: '#6CABFF',
  colorInfoBorderHover: '#7DB5FF',
  colorInfoHover: '#84B9FF',
  colorInfoActive: '#86BAFF',
  colorInfoTextHover: '#7EB6FF',
  colorInfoTextActive: '#82B7FF',
  colorInfoText: '#6CABFF',
  colorHighlight: '#0A62AF',
  colorHighlightWeak: '#2E3B42',
  colorHighlightXWeak: '#2e383d',
  colorHighlightStrong: '#109BCE',
  colorHighlightXStrong: '#3487E6',
  colorLan: '#307BED',
  colorLanWeak: '#436172',
  colorWan: '#2851A5',
  colorWanWeak: '#6CA4F8',
  colorWanPayload: '#7A6EC4',
  colorNetRatio: '#2CC23B',
  colorNetTotal: '#9FADFA',
  colorNetCreated: '#FDC376',
  colorNetDeleted: '#C1A0D1',
  colorNetDenied: '#FADD37',
  colorNetDeniedWeak: '#F8CF83',
  colorAccent: '#6A6A6A',
  colorAccentWeak: '#464545',
  colorAccentXWeak: '#2E2E2E',
  colorAccentXXWeak: '#2c2c2c',
  colorAccentStrong: '#BEBEBE',
  colorAccentXStrong: '#E2E1E1',
  boxShadow: '0px 3px 6px -2px rgba(0,0,0,0.5)',
  boxShadowSecondary: '0px 4px 6px -2px rgba(0,0,0,0.75)',
  alarmOk: '#0daf0d',
  alarmOkBg: '#013801',
  alarmOkBgHover: '#014a01',
  alarmOkBorder: '#0daf0d',
  alarmOkBorderHover: '#0eac0e',
  alarmOkHover: '#0dba0d',
  alarmOkActive: '#0fbd0f',
  alarmOkTextHover: '#0bc20b',
  alarmOkText: '#0daf0d',
  alarmOkTextActive: '#0cb60c',
  alarmUnknown: '#D1C6D7',
  alarmUnknownBg: '#655C6A',
  alarmUnknownBgHover: '#DDCDE5',
  alarmUnknownBorder: '#D1C6D7',
  alarmUnknownBorderHover: '#DBCBE3',
  alarmUnknownHover: '#D7C6E0',
  alarmUnknownActive: '#D6C6DF',
  alarmUnknownTextHover: '#D5BFE1',
  alarmUnknownText: '#D1C6D7',
  alarmUnknownTextActive: '#DCCAE5',
  alarmCritical: '#CE4646',
  alarmCriticalBg: '#360101',
  alarmCriticalBgHover: '#450101',
  alarmCriticalBorder: '#CE4646',
  alarmCriticalBorderHover: '#E15B5B',
  alarmCriticalHover: '#E25959',
  alarmCriticalActive: '#E05C5C',
  alarmCriticalTextHover: '#E55A5A',
  alarmCriticalText: '#FFB4B4',
  alarmCriticalTextActive: '#E35C5C',
  alarmMajor: '#DA8F04',
  alarmMajorBg: '#694502',
  alarmMajorBgHover: '#986301',
  alarmMajorBorder: '#DA8F04',
  alarmMajorBorderHover: '#EA9904',
  alarmMajorHover: '#F4A003',
  alarmMajorActive: '#E89805',
  alarmMajorTextHover: '#E89907',
  alarmMajorText: '#F8A202',
  alarmMajorTextActive: '#EF9F0B',
  alarmMinor: '#D6D604',
  alarmMinorBg: '#9C9C04',
  alarmMinorBgHover: '#B4B407',
  alarmMinorBorder: '#D6D604',
  alarmMinorBorderHover: '#E3E304',
  alarmMinorHover: '#F5F504',
  alarmMinorActive: '#E9E90A',
  alarmMinorTextHover: '#D6D604',
  alarmMinorText: '#E5E504',
  alarmMinorTextActive: '#F2F204',
  alarmWarning: '#03CCCC',
  alarmWarningBg: '#038282',
  alarmWarningBgHover: '#049595',
  alarmWarningBorder: '#03CCCC',
  alarmWarningBorderHover: '#05DBDB',
  alarmWarningHover: '#03E4E4',
  alarmWarningActive: '#05DADA',
  alarmWarningTextHover: '#02EBEB',
  alarmWarningText: '#02E7E7',
  alarmWarningTextActive: '#03DCDC',
  // chartProtocolColorsDark to be updated in future
  colorProtocolTCP: '#8DDDD0',
  colorProtocolUDP: '#FFA056',
  colorProtocolICMP: '#6F4E7C',
  colorProtocolOther: '#CA472F',
  colorProtocolAll: '#0B84A5',
  colorClientLatency: '#907041',
  colorServerLatency: '#70728F',
  colorGradient:
    'linear-gradient(to bottom right, var(--ant-color-brand) 28%, rgba(12, 102, 228, 1) 38%, rgba(31, 19, 46, 1) 50%);',
  chartColors: [
    '#95b9cc',
    '#1c6da3',
    '#a5cf80',
    '#2e9127',
    '#c75702',
    '#04507d',
    '#e8ae64',
    '#bca5c7',
    '#5f378a',
    '#e88e8e',
    '#eb7500',
    '#9e4f23',
    '#787878',
    '#c7c5c5',
    '#bab8b8',
    '#dbabc4',
    '#999999',
    '#ebeb8a',
    '#525252',
    '#e3a805',
    '#8f0334',
    '#7f00cf',
    '#0234bd',
    '#038f8a',
    '#8db528',
    '#d1ce11',
  ],
  mapStyle: '66e8ac2aec78f2c8',
};

export const darkTheme = {
  cssVar: 'true',
  algorithm: darkAlgorithm,
  token,
  components: { ...componentTokens },
};
