import { useEffect, useRef, useState } from 'react';
import { startTransition } from 'react';
import { useSelector } from 'react-redux';
import { Button, Flex, Result, theme } from 'antd';
import PropTypes from 'prop-types';
import { useNavigate, useLocation } from 'react-router-dom';
import monitoringRoutesConstants from '@features/monitoring/monitoring-routes.constants';

import { t } from 'i18next';
import GmsIcon from '@common/components/gms-icon/gms-icon';

const SomethingWentWrongPage = ({ resetErrorBoundary }) => {
  const reduxPreviousRoute = useSelector(
    (state) => state.previousRoute.previousRoute,
  );
  const [previousRoute] = useState(reduxPreviousRoute);
  const location = useLocation();
  const currentLocation = location.pathname;
  const navigate = useNavigate();

  const [countdown, setCountdown] = useState(5);
  const timerRef = useRef(null);
  const hasRedirectedRef = useRef(false);
  const [delay, setDelay] = useState(false);

  const {
    token: { textSecondary, colorBlack },
  } = theme.useToken();

  const suffix = countdown === 1 ? '' : 's';

  const redirect = () => {
    if (hasRedirectedRef.current) return;
    hasRedirectedRef.current = true;

    startTransition(() => {
      if (previousRoute && previousRoute !== currentLocation) {
        navigate(previousRoute);
      } else {
        navigate(monitoringRoutesConstants.SUMMARY.ALARMS.path);
      }
      resetErrorBoundary();
    });
  };

  useEffect(() => {
    let countdownValue = countdown;

    const tick = () => {
      countdownValue -= 1;
      setCountdown(countdownValue);

      if (countdownValue <= 0) {
        redirect();
      } else {
        timerRef.current = setTimeout(tick, 1000);
      }
    };

    timerRef.current = setTimeout(tick, 1000);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const delayTimer = setTimeout(() => setDelay(true), 50);
    return () => clearTimeout(delayTimer);
  }, []);

  return (
    <Flex justify="center" align="center" className="w100 h100">
      {delay && (
        <Result
          icon={
            <GmsIcon name="StatusWarning" color={textSecondary} size="72" />
          }
          title={
            <span style={{ color: colorBlack }}>
              {t('somethingWentWrongTitle')}
            </span>
          }
          subTitle={t('redirectMessage', { count: countdown, suffix })}
          extra={
            <Button type="primary" onClick={redirect}>
              {t('backHomeButton')}
            </Button>
          }
        />
      )}
    </Flex>
  );
};

SomethingWentWrongPage.propTypes = {
  resetErrorBoundary: PropTypes.func.isRequired,
};

export default SomethingWentWrongPage;
