/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { api } from '@sdk';
import {
  AutoComplete,
  Col,
  Collapse,
  Flex,
  Form,
  Row,
  theme,
  Typography,
} from 'antd';
import PropTypes from 'prop-types';
import { isEqual } from 'radash';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@tanstack/react-query';

import {
  ApplianceMessage,
  ChartSpinner,
  FlexContainer,
  GmsChart,
  LegendSelector,
} from '@common/components';
import useTabFilters from '@common/components/tab-filters/use-tab-filters';
import { MONITORING_NS } from '@common/constants';
import { useAppliances, useAppMessage, useUsageStats } from '@common/hooks';
import { suffixFormatter } from '@common/utils';
import { getActualDateRange } from '@common/utils';
import { useTagsData } from '@common/hooks';

import {
  chartTitleMap,
  TOP_APP_COUNT,
} from '../application-bandwidth.constants';
import {
  addAllAppsValue,
  aggregateApplication2Mapper,
  convertToEchartsFormat,
  drawDefaultLegends,
  findMaxValue,
  getDataStructureFromAllApp,
  updateCharts,
} from './application-trends.utils';

function ApplicationTrends(props) {
  const {
    isRefreshed,
    onPageLoad,
    updateChartRef,
    updateChartDateRange,
    updateIsZoomed,
  } = props;
  const [formattedData, setFormattedData] = useState({});
  const { scheduleTimezone } = useTagsData();
  const { allAppliances, selectedAppliancesIds } = useAppliances();
  const [selectApplianceMsg, setSelectApplianceMsg] = useState(null);
  const { t } = useTranslation(MONITORING_NS.APPLICATION_BANDWIDTH);
  const {
    token: { chartColors, colorAccent },
  } = theme.useToken();
  const [form] = Form.useForm();
  const [filterData] = useTabFilters();

  const appMessage = useAppMessage();
  const [chartColorSeries, setChartColorSeries] = useState({});
  const [showSpinners, setShowSpinners] = useState(true);
  const [legends, setLegends] = useState([]);
  const [legendValues, setLegendValues] = useState({});
  const [extraLegends, setExtraLegends] = useState([]);
  const [legendsDropdown, setLegendsDropdown] = useState([]);
  const [defaultAppsData, setDefaultAppsData] = useState([]);
  const [customizedAppNames, setCustomizedAppNames] = useState([]);
  const [panelChange, setPanelChange] = useState(0);
  const prevSelectedItems = useRef(selectedAppliancesIds);

  useUsageStats('Application_Trends_Tab');

  const params = useMemo(() => {
    if (!filterData?.dateFilters) return {};
    updateIsZoomed(false);
    return {
      startTime: filterData?.dateFilters.startTime,
      endTime: filterData?.dateFilters.endTime,
      groupByNE: false,
    };
  }, [filterData, selectedAppliancesIds]);

  const handleButtonClick = useCallback((backendID, selected) => {
    setLegendValues((prevLegendValues) => ({
      ...prevLegendValues,
      ...{
        [backendID]: Object.hasOwn(prevLegendValues, backendID)
          ? !prevLegendValues[backendID]
          : !selected,
      },
    }));
  }, []);

  const handleClose = (appName) => {
    const updatedAppsData = defaultAppsData.map((item) => ({
      ...item,
      data: item.data.filter((dataPoint) => dataPoint.APPNAME !== appName),
    }));
    const updatedCustomizedAppNames = customizedAppNames.filter(
      (name) => name !== appName,
    );
    const updatedExtraLegends = extraLegends.filter(
      (legend) => legend.appName !== appName,
    );
    setCustomizedAppNames(updatedCustomizedAppNames);
    setExtraLegends(updatedExtraLegends);
    setDefaultAppsData(updatedAppsData);
    const updatedLegends = [...legends, ...updatedExtraLegends];
    processHistoricalChartData(updatedAppsData, updatedLegends);
  };

  const handleAppSelect = (selectedName) => {
    if (!legends.some((legend) => legend.appName === selectedName)) {
      const customizedLegends = drawDefaultLegends(
        [selectedName],
        chartColors,
        colorAccent,
        true,
      );
      setCustomizedAppNames((prevNames) => [
        ...prevNames,
        ...customizedLegends.map((legend) => legend.appName),
      ]);
      setExtraLegends((prevLegends) => [...prevLegends, ...customizedLegends]);
      customizedAppNamesMutate({
        payload: { nePks: selectedAppliancesIds },
        params,
        selectedAppName: [selectedName],
      });
      form.setFieldsValue({ [t('searchApplications')]: '' });
    }
  };

  const handlePanelChange = () => {
    setPanelChange((prev) => prev + 1);
  };

  const processHistoricalChartData = (response, legends) => {
    const dateRange = {
      maxTimestamp: Math.max(
        ...response.flatMap((item) => item.data.map((d) => d.TIMESTAMP)),
      ),
      minTimestamp: Math.min(
        ...response.flatMap((item) => item.data.map((d) => d.TIMESTAMP)),
      ),
    };
    const actualDateRange = getActualDateRange(
      params.startTime,
      params.endTime,
      dateRange.minTimestamp,
      dateRange.maxTimestamp,
      filterData?.dateFilters?.granularity,
    );
    if (actualDateRange) {
      const msg = `${t('availableDateRange')} ${actualDateRange.startTime} - ${actualDateRange.endTime}`;
      updateChartDateRange(msg);
    } else {
      updateChartDateRange(null);
    }

    const allAppData = response.shift();
    const processedData = getDataStructureFromAllApp(
      allAppData.data,
      response,
      params,
      scheduleTimezone,
    );
    const chartData = updateCharts(
      legends,
      processedData,
      filterData.lanOrWan === 'LAN' || !filterData.lanOrWan,
      filterData.lanOrWan === 'WAN' || !filterData.lanOrWan,
      allAppliances,
      selectedAppliancesIds,
    );

    const plotData = convertToEchartsFormat(chartData);

    const legendVisibility = chartData.chartLabels.reduce(
      (acc, label) => ({
        ...acc,
        [label]: true,
      }),
      {},
    );

    setShowSpinners(false);
    setFormattedData(plotData);
    setDefaultAppsData([allAppData, ...response]);
    setChartColorSeries(chartData.chartColorSeries);
    setLegendValues(legendVisibility);
  };

  const fetchAppData = async (payload, params, appNames) => {
    const wholeData = [];
    for (const appName of appNames) {
      const appData = await api.stats.getAppData(payload, {
        params: {
          startTime: params.startTime,
          endTime: params.endTime,
          application: appName,
        },
      });
      wholeData.push(appData.data);
    }
    return wholeData;
  };

  const useBatchMutations = async ({ payload, params }) => {
    setShowSpinners(true);
    updateChartDateRange(null);
    setLegendsDropdown(
      (await api.appDefinition.getAppNamesAndUpdateList({ pattern: '' })).data,
    );

    const defaultApps = await api.stats.postAggregateAppData(payload, {
      params: {
        startTime: params.startTime,
        endTime: params.endTime,
        groupByNE: false,
        top: TOP_APP_COUNT,
      },
    });
    if (!defaultApps.data.length) return [];

    const appNames = aggregateApplication2Mapper(defaultApps.data);
    setLegends(drawDefaultLegends(appNames, chartColors, colorAccent));
    setCustomizedAppNames([]);
    setExtraLegends([]);

    const wholeData = await fetchAppData(payload, params, appNames);
    return wholeData;
  };

  const { mutate } = useMutation({
    mutationFn: useBatchMutations,
    onSuccess: (res) => {
      if (res && res.length && legends.length) {
        processHistoricalChartData(res, legends);
      } else if (res && res.length === 0) {
        const noData = {
          inLan: [],
          inWan: [],
          outLan: [],
          outWan: [],
        };
        const charts = allAppliances.reduce((acc, appliance) => {
          if (selectedAppliancesIds.includes(appliance.nePk)) {
            acc[appliance.hostName] = noData;
          }
          return acc;
        }, {});
        setShowSpinners(false);
        setFormattedData(charts);
        onPageLoad({
          isSuccess: true,
          lastRefreshed: new Date().getTime(),
        });
        filterData.dateFilters.timeSegment === 'Custom' &&
          updateChartDateRange(t('noDataAvailable'));
      }
    },
    onError: (err) => {
      setShowSpinners(false);
      appMessage.error({
        message: `${t('pageTitle')} - ${err?.response?.data || err?.message || ''}`,
      });
    },
  });

  const { mutate: customizedAppNamesMutate } = useMutation({
    mutationFn: async ({ payload, params, selectedAppName }) =>
      fetchAppData(payload, params, selectedAppName),
    onSuccess: (res) => {
      if (res && res.length && extraLegends.length) {
        const updatedWholeData = [...defaultAppsData, ...res];
        processHistoricalChartData(updatedWholeData, [
          ...legends,
          ...extraLegends,
        ]);
      }
    },
    onError: ({ response }) => {
      setShowSpinners(false);
      appMessage.error({
        message: `${t('pageTitle')} - ${response.data}`,
      });
    },
  });

  useEffect(() => {
    setSelectApplianceMsg(
      selectedAppliancesIds.length ? null : t('noApplianceMsg'),
    );
    if (selectedAppliancesIds.length) {
      mutate({
        payload: { nePks: selectedAppliancesIds },
        params,
      });
    }
  }, [isRefreshed]);

  useEffect(() => {
    setSelectApplianceMsg(
      selectedAppliancesIds.length ? null : t('noApplianceMsg'),
    );
    if (
      !selectedAppliancesIds.length ||
      (isEqual(
        prevSelectedItems.current['selectedAppliancesIds'],
        selectedAppliancesIds,
      ) &&
        isEqual(prevSelectedItems.current['params'], params))
    ) {
      return; // Exit early if conditions are not met
    }
    mutate({
      payload: { nePks: selectedAppliancesIds },
      params,
    });
    prevSelectedItems.current['params'] = params;
    prevSelectedItems.current['selectedAppliancesIds'] = selectedAppliancesIds;
  }, [selectedAppliancesIds, params, mutate]);

  const renderChartSpinners = () => {
    const charts = [];
    const activeKeys = [];

    allAppliances.forEach((appliance) => {
      const rowCharts = [];

      if (selectedAppliancesIds.includes(appliance.nePk)) {
        for (let chartIndex = 0; chartIndex < 4; chartIndex++) {
          // 4 charts per appliance
          rowCharts.push(
            <Col span={12} key={`${appliance.nePk}-${chartIndex}`}>
              <ChartSpinner key={chartIndex} spinnerRef={{ current: 'div' }} />
            </Col>,
          );
        }

        charts.push(
          <Collapse.Panel
            header={appliance.hostName}
            key={`${appliance.nePk}-collapse`}
            style={{ marginBottom: 10 }}>
            <Row>{rowCharts}</Row>
          </Collapse.Panel>,
        );

        activeKeys.push(`${appliance.nePk}-collapse`);
      }
    });

    return (
      <Col span={19}>
        <Collapse defaultActiveKey={activeKeys} expandIconPosition="end">
          {charts}
        </Collapse>
      </Col>
    );
  };

  const renderLegends = () => {
    return Object.entries(chartColorSeries).map(([label, { color }], index) => {
      const legendOptions =
        [...legends, ...extraLegends].find(
          (options) => label == options.appName,
        ) || {};
      return (
        <Flex key={index} style={{ display: 'block' }}>
          <LegendSelector
            id={`legend-${label}`}
            key={index}
            label={label}
            backendID={label}
            color={color}
            type="solid"
            side="left"
            isExtra={legendOptions.extra || false}
            handleClose={handleClose}
            selected={legendValues[label]}
            handleButtonClick={handleButtonClick}
          />
        </Flex>
      );
    });
  };

  const renderChart = () => {
    const chartGroups = [];
    let rowCharts = [];
    const isEmptyData = Object.keys(formattedData).length === 0;
    const maxValue = isEmptyData
      ? 0
      : findMaxValue(formattedData, legendValues);
    const hostnames = isEmptyData
      ? allAppliances
          .filter((appliance) => selectedAppliancesIds.includes(appliance.nePk))
          .map((appliance) => appliance.hostName)
      : Object.keys(formattedData);

    for (const hostname of hostnames) {
      const charts = [];
      const hostCharts = isEmptyData
        ? {
            inLan: [],
            outLan: [],
            inWan: [],
            outWan: [],
          }
        : formattedData[hostname];

      for (const chartName in hostCharts) {
        const chartData = addAllAppsValue(hostCharts[chartName]);
        if (filterData.lanOrWan === 'LAN' && chartName.includes('Wan'))
          continue;
        if (filterData.lanOrWan === 'WAN' && chartName.includes('Lan'))
          continue;

        rowCharts.push(
          <Col
            {...(filterData.large === 'expand'
              ? { xs: 24 }
              : { xs: 24, sm: 16, md: 14, lg: 12 })}
            key={`${hostname}-${t(chartTitleMap[chartName])}+${panelChange}`}>
            <GmsChart
              title={`${t(chartTitleMap[chartName], { titleCase: true })}`}
              series={chartData}
              chartRef={{ current: 'div' }}
              updateChartRef={updateChartRef}
              tooltipValueFormatter={suffixFormatter}
              yAxisLabelFormatter={suffixFormatter}
              isLockScale={filterData.lockScale}
              selectedLegends={legendValues}
              maxValue={maxValue}
              isLarge={filterData.large === 'expand'}
              isUTC={filterData.utc}
              query={params}
              showNoData={true}
              onZoom={updateIsZoomed}
              {...(filterData.large === 'expand' && {
                gridStyles: {
                  left: '7%',
                  right: '3%',
                },
                titleStyles: {
                  left: '6%',
                },
              })}
            />
          </Col>,
        );

        if (rowCharts.length === 2) {
          charts.push(
            <Row key={`${hostname}-${chartName}-row`}>{rowCharts}</Row>,
          );
          rowCharts = [];
        }
      }

      chartGroups.push(
        <Collapse.Panel header={hostname} key={hostname}>
          {charts}
        </Collapse.Panel>,
      );
    }

    // If there are any remaining charts, add them in a final row
    if (rowCharts.length > 0) {
      chartGroups.push(<Row key="last-row">{rowCharts}</Row>);
    }

    // Get the keys of all hostnames for the defaultActiveKey
    const hostNames = Object.keys(formattedData);
    const defaultActiveKey = hostNames.length > 0 ? [hostNames[0]] : [];

    return (
      <Row gutter={12}>
        <Col span={19}>
          <Collapse
            expandIconPosition="end"
            defaultActiveKey={defaultActiveKey}
            onChange={handlePanelChange}>
            {chartGroups}
          </Collapse>
        </Col>
        <Col span={5}>
          <Col
            className="stickyTop"
            style={{
              maxHeight: '100vh',
            }}>
            <Typography.Title>{t('top10Apps')}</Typography.Title>
            {renderLegends()}
            <Form form={form}>
              <Form.Item name={t('searchApplications')}>
                <AutoComplete
                  options={legendsDropdown?.map((option) => ({
                    value: option,
                  }))}
                  filterOption={(input, option) =>
                    (option?.value ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  placeholder={t('searchApplications')}
                  style={{ width: '60%' }}
                  onSelect={handleAppSelect}
                />
              </Form.Item>
            </Form>
          </Col>
        </Col>
      </Row>
    );
  };

  return (
    <>
      {!selectApplianceMsg ? (
        <FlexContainer className="overflowAuto overflowXHidden">
          {showSpinners ? renderChartSpinners() : renderChart()}
        </FlexContainer>
      ) : (
        <ApplianceMessage message={selectApplianceMsg} />
      )}
    </>
  );
}

ApplicationTrends.propTypes = {
  route: PropTypes.string,
  isRefreshed: PropTypes.bool,
  updateIsZoomed: PropTypes.func,
  updateChartRef: PropTypes.func,
  updateChartDateRange: PropTypes.func,
  onPageLoad: PropTypes.func,
  isReRender: PropTypes.bool,
};

export { ApplicationTrends };
