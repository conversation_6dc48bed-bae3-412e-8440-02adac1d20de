import { createListenerMiddleware } from '@reduxjs/toolkit';
import { setLocalStorageItem } from '@common/utils';
import {
  localStorageSlices,
  localStorageSlicesKeys,
} from './localstorage-slices.constants';

export const localStorageMiddleware = createListenerMiddleware();

localStorageMiddleware.startListening({
  predicate: ({ type }) => {
    const slice = type.split('/')[0];

    return (
      localStorageSlices.includes(slice) ||
      localStorageSlicesKeys.includes(type)
    );
  },
  effect: ({ type, payload = {} }, { getState }) => {
    const slice = type.split('/')[0];

    if (localStorageSlices.includes(slice)) {
      const state = getState();
      setLocalStorageItem(`redux:${slice}`, state[slice]);
    } else if (localStorageSlicesKeys.includes(type)) {
      setLocalStorageItem(`redux:${type}`, payload);
    }
  },
});
