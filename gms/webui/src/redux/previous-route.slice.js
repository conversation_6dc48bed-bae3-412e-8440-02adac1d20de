// redux/previous-route.slice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  previousRoute: '', // Just a string, not an object
};

const previousRouteSlice = createSlice({
  name: 'previousRoute',
  initialState,
  reducers: {
    setPreviousRoute(state, action) {
      state.previousRoute = action.payload; // Save the route as a string
    },
  },
});

export const { setPreviousRoute } = previousRouteSlice.actions;
export default previousRouteSlice.reducer;
