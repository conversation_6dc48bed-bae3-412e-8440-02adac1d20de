import { configureStore } from '@reduxjs/toolkit';
import tabFiltersReducers from '@common/components/tab-filters/tab-filters.slice';
import authReducer from '@features/login/auth.slice.js';
import tabsReducer from '@features/app-layout/app-tabs/app-tabs.slice.js';
import modalReducer from '@features/app-layout/app-modal-manager/app-modal-manager.slice.js';
import mainMenuReducer from '@features/app-layout/app-menu/app-menu.slice';
import createCaseReducer from '@features/support/technical-assistance/create-case-modal/create-case-modal.slice';
import rmaWizardReducer from '@features/support/technical-assistance/rma-wizard-modal/rma-wizard.slice';
import templateAndPoliciesProducer from '@features/configuration/templates-policies/template.slice';
import notificationReducer from '@features/app-layout/app-notification-manager/app-notification-manager.slice.js';
import applicationDefinitionsReducer from '@features/configuration/templates-policies/applications-and-saas/application-definitions/application-definitions.slice';
import notificationHeaderReducer from '@features/app-layout/notification-header/notification-header.slice';
import pageHeaderReducer from '@common/components/page-header/page-header.slice.js';
import previousRouteReducer from './previous-route.slice';
import sidebarReducer from '@features/app-layout/sidebar/sidebar.slice';
import matchCriteriaReducer from '@features/configuration/templates-policies/templates/match-criteria/match-criteria.slice';

import { localStorageMiddleware } from './middlewares/localstorage/localstorage.middleware';
import { combineReducers } from '@reduxjs/toolkit';

const combinedReducer = combineReducers({
  auth: authReducer,
  appTabs: tabsReducer,
  appModal: modalReducer,
  createCase: createCaseReducer,
  mainMenu: mainMenuReducer,
  templatesAndPolicies: templateAndPoliciesProducer,
  rmaWizard: rmaWizardReducer,
  tabFilters: tabFiltersReducers,
  notifications: notificationReducer,
  applicationDefinitions: applicationDefinitionsReducer,
  notificationHeader: notificationHeaderReducer,
  pageHeader: pageHeaderReducer,
  previousRoute: previousRouteReducer,
  sidebar: sidebarReducer,
  matchCriteria: matchCriteriaReducer,
});

//create a root reducer to provide a central way of clearing redux cache upon user logout
const rootReducer = (state, action) => {
  if (action.type === 'logout') {
    state = undefined; //setting the state to undefined resets all stores to their initial state
  }
  return combinedReducer(state, action);

  //TODO: there are some incorrect cases of dispatch() throughout the code, eg in AppLayout. Have to assign to owners to fix in the future
};

const storeSetup = (preloadedState) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState,
    middleware: () => [localStorageMiddleware.middleware],
  });
};

const store = storeSetup();

export { store as default, storeSetup };
