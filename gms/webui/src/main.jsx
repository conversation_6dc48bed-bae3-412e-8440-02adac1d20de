import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { App, ConfigProvider } from 'antd';
import { Provider } from 'react-redux';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// IMP - This import need to be first one as it initialized the API SDK
import '@config/sdk';

import { theme } from '@theme';
import router from './router.jsx';
import store from './redux/store.js';
import { configureAgGrid } from '@config/ag-grid/ag-grid.js';
import { queryClient } from '@config/query/query.config.js';
import '@config/i18n/i18n.config';

import './index.scss';

const fnToProxy = 'error';
const originalErrorFn = console[fnToProxy].bind(console);
console[fnToProxy] = (...args) => {
  const err = new Error();
  const isLicenseError =
    err.stack.includes('license') ||
    err.stack.includes('trial') ||
    err.stack.includes('outputMissingLicenseKey');

  if (!isLicenseError) {
    originalErrorFn.apply(this, [
      `[${fnToProxy}]: ${args[0]}`,
      ...args.splice(1, args.length),
    ]);
  }
};
configureAgGrid();

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <Provider store={store}>
      <ConfigProvider theme={theme}>
        <QueryClientProvider client={queryClient}>
          <App>
            <RouterProvider router={router} />
          </App>

          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ConfigProvider>
    </Provider>
  </React.StrictMode>,
);
