/* 
    Since react follows JS naming conventions for CSS (which is annoying. -cw)
    Collection of global generic use case layout utility classes in camel-case format below.
    Todo: identify any additional media query considerations for print, responsive, etc.
*/
ul.reset,
ol.reset,
ul.reset li,
ol.reset li,
ul.reset ul li,
ol.reset ul li {
  margin: 0;
  padding: 0;
  text-indent: 0;
  list-style-type: none;
}
.clearfix::after {
  display: block;
  clear: both;
  content: '';
}
.hStack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}
.vStack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}
.visuallyHidden,
.visuallyHiddenFocusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.textTruncate,
.textEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.alignBaseline {
  vertical-align: baseline !important;
}
.alignTop {
  vertical-align: top !important;
}
.alignMiddle {
  vertical-align: middle !important;
}
.alignBottom {
  vertical-align: bottom !important;
}
.alignTextBottom {
  vertical-align: text-bottom !important;
}
.alignTextTop {
  vertical-align: text-top !important;
}
.floatStart {
  float: left !important;
}
.floatEnd {
  float: right !important;
}
.floatNone {
  float: none !important;
}
.opacity0 {
  opacity: 0 !important;
}
.opacity25 {
  opacity: 0.25 !important;
}
.opacity50 {
  opacity: 0.5 !important;
}
.opacity75 {
  opacity: 0.75 !important;
}
.opacity100 {
  opacity: 1 !important;
}
.overflowAuto {
  overflow: auto !important;
}
.overflowYAuto {
  overflow-y: auto;
}
.overflowXAuto {
  overflow-x: auto;
}
.overflowHidden {
  overflow: hidden !important;
}
.overflowXHidden {
  overflow-x: hidden !important;
}
.overflowYHidden {
  overflow-y: hidden !important;
}
.overflowVisible {
  overflow: visible !important;
}
.overflowScroll {
  overflow: scroll !important;
}
.dInline {
  display: inline !important;
}
.dInlineBlock {
  display: inline-block !important;
}
.dBlock {
  display: block !important;
}
.dGrid {
  display: grid !important;
}
.dTable {
  display: table !important;
}
.dTableRow {
  display: table-row !important;
}
.dTableCell {
  display: table-cell !important;
}
.dFlex {
  display: flex !important;
}
.dInlineFlex {
  display: inline-flex !important;
}
.dNone {
  display: none !important;
}
.shadowNone {
  box-shadow: none !important;
}
.positionStatic {
  position: static !important;
}
.positionRelative {
  position: relative !important;
}
.positionAbsolute {
  position: absolute !important;
}
.positionFixed {
  position: fixed !important;
}
.positionSticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}
.stickyTop {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}
.top0 {
  top: 0 !important;
}
.top50 {
  top: 50% !important;
}
.top100 {
  top: 100% !important;
}
.bottom0 {
  bottom: 0 !important;
}
.bottom50 {
  bottom: 50% !important;
}
.bottom100 {
  bottom: 100% !important;
}
.start0 {
  left: 0 !important;
}
.start50 {
  left: 50% !important;
}
.start100 {
  left: 100% !important;
}
.end0 {
  right: 0 !important;
}
.end50 {
  right: 50% !important;
}
.end100 {
  right: 100% !important;
}
.border0 {
  border: 0 !important;
}
.borderTop0 {
  border-top: 0 !important;
}
.borderRight0 {
  border-right: 0 !important;
}
.borderBottom0 {
  border-bottom: 0 !important;
}
.borderLeft0 {
  border-left: 0 !important;
}
.w25 {
  width: 25% !important;
}
.w30 {
  width: 30% !important;
}
.w50 {
  width: 50% !important;
}
.w75 {
  width: 75% !important;
}
.w100 {
  width: 100% !important;
}
.wAuto {
  width: auto !important;
}
.h25 {
  height: 25% !important;
}
.h50 {
  height: 50% !important;
}
.h75 {
  height: 75% !important;
}
.h88 {
  height: 88% !important;
}
.h98 {
  height: 98% !important;
}
.h100 {
  height: 100% !important;
}
.hAuto {
  height: auto !important;
}
.flexFill {
  flex: 1 1 auto !important;
}
.flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flexRow {
  flex-direction: row !important;
}
.flexColumn {
  flex-direction: column !important;
}
.flexRowReverse {
  flex-direction: row-reverse !important;
}
.flexColumnReverse {
  flex-direction: column-reverse !important;
}
.flexGrow0 {
  flex-grow: 0 !important;
}
.flexGrow1 {
  flex-grow: 1 !important;
}
.flexShrink0 {
  flex-shrink: 0 !important;
}
.flexShrink1 {
  flex-shrink: 1 !important;
}
.flexWrap {
  flex-wrap: wrap !important;
}
.flexNoWrap {
  flex-wrap: nowrap !important;
}
.flexWrapReverse {
  flex-wrap: wrap-reverse !important;
}
.justifyContentStart {
  justify-content: flex-start !important;
}
.justifyContentEnd {
  justify-content: flex-end !important;
}
.justifyContentCenter {
  justify-content: center !important;
}
.justifyContentBetween {
  justify-content: space-between !important;
}
.justifyContentAround {
  justify-content: space-around !important;
}
.justifyContentEvenly {
  justify-content: space-evenly !important;
}
.alignItemsStart {
  align-items: flex-start !important;
}
.alignItemsEnd {
  align-items: flex-end !important;
}
.alignItemsCenter {
  align-items: center !important;
}
.alignItemsBaseline {
  align-items: baseline !important;
}
.alignItemsStretch {
  align-items: stretch !important;
}
.alignContentStart {
  align-content: flex-start !important;
}
.alignContentEnd {
  align-content: flex-end !important;
}
.alignContentCenter {
  align-content: center !important;
}
.alignContentBetween {
  align-content: space-between !important;
}
.alignContentAround {
  align-content: space-around !important;
}
.alignContentStretch {
  align-content: stretch !important;
}
.alignSelfAuto {
  align-self: auto !important;
}
.alignSelfStart {
  align-self: flex-start !important;
}
.alignSelfEnd {
  align-self: flex-end !important;
}
.alignSelfCenter {
  align-self: center !important;
}
.alignSelfBaseline {
  align-self: baseline !important;
}
.alignSelfStretch {
  align-self: stretch !important;
}
.orderFirst {
  order: -1 !important;
}
.order0 {
  order: 0 !important;
}
.order1 {
  order: 1 !important;
}
.order2 {
  order: 2 !important;
}
.order3 {
  order: 3 !important;
}
.order4 {
  order: 4 !important;
}
.order5 {
  order: 5 !important;
}
.orderLast {
  order: 6 !important;
}
.textLeft {
  text-align: left !important;
}
.textRight {
  text-align: right !important;
}
.textCenter {
  text-align: center !important;
}
.textDecorationNone {
  text-decoration: none !important;
}
.textUnderline {
  text-decoration: underline !important;
}
.textStrike {
  text-decoration: line-through !important;
}
.textDecorationLineThrough {
  text-decoration: line-through !important;
}
.textLowercase {
  text-transform: lowercase !important;
}
.textUppercase {
  text-transform: uppercase !important;
}
.textCapitalize {
  text-transform: capitalize !important;
}
.noTextTransform {
  text-transform: unset !important;
}
.textWrap {
  white-space: normal !important;
}
.textNoWrap {
  white-space: nowrap !important;
}
.textBreak {
  word-wrap: break-word !important;
  word-break: break-word !important;
}
.visible {
  visibility: visible !important;
}
.invisible {
  visibility: hidden !important;
}
.userSelectNone {
  -webkit-user-select: none !important;
  user-select: none !important;
}
.srOnly {
  position: absolute;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 0;
  border: 0;
  height: 0;
  width: 0;
  overflow: hidden;
}
.rotate90 {
  transform: rotate(90deg);
}
.rotate90Reverse {
  transform: rotate(-90deg);
}
.rotate180 {
  transform: rotate(180deg);
}

.uidebug {
  outline: red 1px dashed;
}
