.ag-theme-orchestrator {
  /* 
        `grid-size` = Seed value defines sizing in multiples via px value, can effect various spacing and paddings.
        If changed will likely require editing `row-height` & `list-item-height` accordingly.
        Ref: https://ag-grid.com/javascript-data-grid/global-style-customisation-variables/
    */

  --ag-grid-size: 8px;
  --ag-list-item-height: var(--ant-control-height);

  --ag-font-size: var(--ant-font-size);
  --ag-font-family: var(--ant-font-family);

  // --ag-icon-font-display: none;
  --ag-icon-size: var(--ant-font-size);
  --ag-icon-font-weight: normal;
  --ag-icon-font-color: var(--ant-color-text);
  --ag-icon-font-family: agGridQuartz;
  --ag-icon-font-color-checkbox-checked: var(--ant-color-white);
  // --ag-icon-font-color-checkbox-unchecked: red;

  --ag-active-color: var(--ant-color-primary);

  /* Replaced by orch theme but kept for reference. -cw
    --ag-alpine-active-color: var(--ag-active-color);
    --ag-balham-active-color: var(--ag-active-color);
    --ag-material-primary-color: var(--ag-active-color);
    --ag-material-accent-color: var(--ag-active-color); 
    */

  --ag-foreground-color: var(--ant-color-text);
  --ag-background-color: var(--ant-layout-body-bg);
  --ag-secondary-foreground-color: var(--ant-color-text-secondary);
  --ag-disabled-foreground-color: var(--ant-color-text-disabled);
  --ag-data-color: var(--ag-foreground-color);
  --ag-invalid-color: var(--ant-color-error);
  --ag-tooltip-background-color: var(--ant-color-bg-elevated);
  --ag-chip-background-color: var(--ant-color-accent);
  --ag-chip-border-color: color-mix(
    in srgb,
    var(--ant-layout-body-bg, --ant-color-text-base) 13%
  );

  --ag-tab-min-width: 250px;
  --ag-side-bar-panel-width: 250px;

  --ag-selected-row-background-color: var(--ant-color-highlight);
  --ag-selected-tab-underline-color: var(--ant-color-primary);
  /* 
    --ag-selected-tab-underline-width: ;
    --ag-selected-tab-underline-transition-speed: ;
    */

  --ag-wrapper-border-radius: 0; // var(--ant-border-radius);
  --ag-border-color: var(--ant-color-border-secondary);
  --ag-border-radius: var(--ant-border-radius);
  --ag-borders: solid 1px;
  --ag-borders-side-button: none;
  --ag-borders-input: var(--ant-border) 1px solid;
  --ag-borders-input-invalid: var(--ant-error) 1px solid;
  --ag-borders-critical: var(--ag-borders);
  --ag-borders-secondary: var(--ag-borders);
  --ag-secondary-border-color: var(--ant-color-border-secondary);
  --ag-row-loading-skeleton-effect-color: var(--ant-color-accent-weak);

  --ag-value-change-value-highlight-background-color: var(
    --ant-color-highlight
  );
  --ag-value-change-delta-up-color: var(--ant-color-success);
  --ag-value-change-delta-down-color: var(--ant-color-error);

  --ag-header-height: var(--ant-control-height-lg);
  --ag-header-foreground-color: var(--ag-foreground-color);
  --ag-header-background-color: var(--ant-color-bg-elevated);
  --ag-header-cell-hover-background-color: var(--ant-color-highlight-weak);
  --ag-header-cell-moving-background-color: var(--ant-color-accent-strong);
  --ag-header-column-separator-color: var(--ant-color-border);
  --ag-header-column-separator-height: 100%;
  --ag-header-column-separator-width: 1px;
  /* --ag-header-column-separator-display: ; */
  --ag-header-column-resize-handle-color: var(--ant-color-border);
  --ag-header-column-resize-handle-height: 100%;
  --ag-header-column-resize-handle-width: 1px;
  --ag-header-column-resize-handle-display: block;

  --ag-subheader-background-color: var(--ant-color-bg-elevated);
  --ag-subheader-toolbar-background-color: var(--ant-color-bg-elevated);

  --ag-row-group-indent-size: var(--ant-padding-sm);
  --ag-row-border-style: solid;
  --ag-row-border-width: 1px;
  --ag-row-height: var(--ant-control-height);
  --ag-row-border-color: var(--ant-color-border-secondary);
  --ag-row-hover-color: var(--ant-color-highlight);
  --ag-odd-row-background-color: var(--ant-color-highlight-x-weak);

  --ag-cell-horizontal-padding: var(--ant-padding-sm);
  --ag-cell-horizontal-border: var(--ant-line-width) solid transparent;
  --ag-cell-widget-spacing: calc(var(--ag-grid-size) * 1.5);

  --ag-column-select-indent-size: var(--ant-padding);
  --ag-column-hover-color: var(--ant-color-accent-x-weak);

  --ag-menu-min-width: 188px;
  --ag-menu-border-color: var(--ant-color-border);
  --ag-menu-background-color: var(--ant-color-bg-layout);

  --ag-panel-border-color: var(--ant-color-border);
  --ag-panel-background-color: var(--ant-color-bg-container);

  --ag-input-focus-border-color: var(--ant-color-primary-active);
  --ag-input-focus-box-shadow: 0 0 3px 3px var(--ant-color-fill-secondary);
  --ag-input-disabled-border-color: var(--ant-color-text-disabled);
  --ag-input-disabled-background-color: var(--ant-bg-container-disabled);
  --ag-input-border-color: var(--ant-color-border);
  --ag-input-border-color-invalid: var(--ant-color-error);

  --ag-checkbox-background-color: var(--ant-color-bg-elevated);
  --ag-checkbox-checked-color: var(--ant-color-primary);
  --ag-checkbox-unchecked-color: var(--ant-color-border-secondary);
  --ag-checkbox-indeterminate-color: var(--ant-alarm-unknown);
  --ag-checkbox-border-radius: var(--ant-border-radius);

  --ag-toggle-button-border-width: 1px;
  --ag-toggle-button-on-border-color: var(--ant-color-primary);
  --ag-toggle-button-off-border-color: var(--ant-color-accent-strong);
  --ag-toggle-button-on-background-color: var(--ant-color-primary);
  --ag-toggle-button-off-background-color: var(--ant-color-accent-strong);
  --ag-toggle-button-switch-background-color: var(--ant-color-bg-container);
  --ag-toggle-button-switch-border-color: var(--ant-color-bg-container);
  --ag-toggle-button-width: 26px;
  --ag-toggle-button-height: 16px;

  --ag-chart-menu-panel-width: 260px;
  --ag-chart-menu-label-color: var(--ant-color-text);
  --ag-chart-menu-pill-select-button-color: var(--ant-color-text);
  --ag-minichart-selected-chart-color: var(--ant-color-highlight);
  --ag-minichart-selected-page-color: var(--ant-color-primary);

  --ag-widget-container-horizontal-padding: var(--ant-padding);
  --ag-widget-container-vertical-padding: var(--ant-padding);
  --ag-widget-horizontal-spacing: var(--ant-padding-sm);
  --ag-widget-vertical-spacing: var(--ant-padding-xs);

  --ag-card-radius: var(--ant-border-radius-sm);
  --ag-card-shadow: var(--ant-box-shadow-card);

  --ag-range-selection-border-color: var(--ant-color-border);
  --ag-range-selection-background-color: var(--ant-blue-4);
  --ag-range-selection-background-color-2: var(--ant-blue-5);
  --ag-range-selection-background-color-3: var(--ant-blue-6);
  --ag-range-selection-background-color-4: var(--ant-blue-7);
  --ag-range-selection-highlight-color: var(--ant-color-highlight);
  --ag-range-selection-chart-category-background-color: var(
    --ant-color-highlight-x-weak
  );
  --ag-range-selection-chart-background-color: var(--ant-color-bg-layout);

  --ag-filter-tool-panel-group-indent: var(--ant-padding);
  --ag-set-filter-indent-size: var(--ant-padding);
  --ag-advanced-filter-builder-indent-size: var(--ant-padding);

  // CW TODO: review these colors with DMerwin as is new functionality, placeholders for now.
  --ag-advanced-filter-join-pill-color: var(--ant-color-error-bg);
  --ag-advanced-filter-column-pill-color: var(--ant-color-success-bg);
  --ag-advanced-filter-option-pill-color: var(--ant-color-warning-bg);
  --ag-advanced-filter-value-pill-color: var(--ant-color-info-bg);

  --ag-control-panel-background-color: var(--ant-bg-elevated);
  --ag-modal-overlay-background-color: var(--ant-color-bg-mask);
  --ag-side-button-selected-background-color: var(--ant-color-primary-active);
  --ag-popup-shadow: var(--ant-box-shadow);
}
