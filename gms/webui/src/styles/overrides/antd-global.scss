// * Overrides only necessary IF no AntD Design Token available.

// Restrict non input text from highlighting / interacting in ant comps that dont need it.
.css-var-re {
  user-select: none;
}

.ant-tabs-tab.ant-tabs-tab-active {
  border-top-color: var(--ant-color-primary-border-active) !important;
}

.ant-tabs-tab[role='button']:hover {
  border-top-color: var(--ant-color-primary-border-hover);
  background-color: var(--ant-color-accent-x-weak);
}

// AntD Icon component explicitly didn't have supported tokens yet and `currentColor` isn't actually refd, if they add support please refactor out. -cw
button.ant-btn.ant-btn-icon-only svg path {
  stroke: currentColor !important;

  &:hover {
    stroke: var(--ant-color-primary-hover) !important;
  }
}

.ant-tabs-nav-wrap {
  padding-left: var(--ant-padding-md);
}

// The "more" button that appears on the tabs when overflow.
button.ant-tabs-nav-more {
  border-left: var(--ant-color-border) 1px solid !important;
}

// Compromise since the `bottom` attribute doesnt appear mapped like the docs say
.ant-notification.ant-notification-bottom {
  inset: auto auto 0 50% !important;
}

// Expanding AntD notification options
.ant-notification-notice-wrapper .ant-notification-notice {
  border-width: 1px;
  border-style: solid;
  border-radius: var(--ant-border-radius);

  &.ant-notification-notice-error {
    border-color: var(--ant-color-error);
  }
  &.ant-notification-notice-success {
    border-color: var(--ant-color-success);
  }
  &.ant-notification-notice-warning {
    border-color: var(--ant-color-warning);
  }
  &.ant-notification-notice-info {
    border-color: var(--ant-color-info);
  }
}

// Explicitly to customize our general modals.
.gmsModal {
  & button.ant-modal-close {
    top: var(--ant-margin-sm);
  }

  &.gmsWarning .ant-modal-content {
    border: var(--ant-color-warning-border) 1px solid;
  }

  &.gmsInfo .ant-modal-content {
    border: var(--ant-color-info-border) 1px solid;
  }
}

// Let active tab retain context with its content.
.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active {
  background: var(--ant-color-bg-layout) !important;
  border-bottom: 0 !important;
}

// For hover close btn's on tabs
.ant-tabs-tab {
  user-select: none;

  & .ant-tabs-tab-btn {
    margin-left: var(--ant-margin-xs);
    margin-right: var(--ant-margin-sm);
  }

  & button[aria-label='remove'] {
    opacity: 0;
    position: absolute;
    right: 0;
    font-size: var(--ant-tabs-title-font-size);
    padding: var(--ant-padding-xs);
    transition: opacity var(--ant-motion-duration-mid) linear;

    &:hover,
    &:active,
    &:focus {
      color: var(--ant-color-primary-hover);
      background-color: var(--ant-color-bg-elevated);
    }
  }

  &:hover,
  &:active,
  &:focus,
  &:focus-within {
    & button[aria-label='remove'] {
      opacity: 1;
    }
  }
}

// popover titles
.ant-popover .ant-popover-title {
  font-size: var(--ant-font-size-heading-3);
}

// steps component untokenized alignments
.ant-steps .ant-steps-item .ant-steps-item-title:after {
  left: 107%;
  width: 100%;
}

// form labels only to improve contextual proximity.
.ant-form .ant-form-item .ant-form-item-label label {
  line-height: var(--ant-font-size-sm);
}

// align checkbox with first-line of label in forms
.ant-form-item-control-input .ant-checkbox-wrapper .ant-checkbox {
  align-self: flex-start;
  margin-top: var(--ant-margin-xs);
}

.appNavMenu {
  & .ant-menu-submenu-title {
    font-size: var(--ant-font-size-heading-2);
  }
}

// Because ant hasn't mapped all their attributes to tokens.
.ant-segmented .ant-segmented-item-selected {
  // background-color: var(--ant-color-primary-bg);
  border: var(--ant-color-primary) 1px solid;
  transition: border-color var(--ant-motion-duration-fast);
  &:hover,
  &:focus {
    border-color: var(--ant-color-primary-active);
  }
}
// Because grommet icons don't play nice in segment component
.ant-segmented .ant-segmented-item-label,
.ant-segmented .ant-segmented-item-label .ant-segmented-item-icon {
  display: flex;
  align-items: center;
}
// to show the selected item as disabled when the date filter is disabled
.ant-segmented-item-selected.ant-segmented-item-disabled {
  box-shadow: none !important;
  border: 1px solid transparent !important;
}
// Visual indicator on collapse (default nor tokens provide)
.ant-collapse {
  & .ant-collapse-header[role='button'] {
    &:focus,
    &:hover {
      background-color: var(--ant-color-highlight-weak);
    }
  }

  & .ant-collapse-header[aria-expanded='true'] {
    background-color: var(--ant-color-accent-xx-weak);
  }
}

.ant-collapse .ant-collapse-header {
  padding-left: var(--ant-padding) !important;
  padding-right: var(--ant-padding) !important;
}

// CW TODO NOTE: remove everything below when new nav tree is introduced.
.appliance-tree.ant-tree {
  flex: 1;
  overflow: auto;
  --ant-tree-indent-size: 0;

  & .ant-tree-treenode {
    padding: 0px;

    & .npkNavNode {
      position: relative;
      height: var(--ant-control-height);
      padding-left: var(--ant-padding-lg);

      // & > .npkNavNodeActions {
      //   display: none;
      //   position: absolute;
      //   right: 0;
      //   top: 0;
      //   bottom: 0;
      //   border-left: var(--ant-color-border) 1px solid;
      //   background-color: var(--ant-color-highlight);
      // }
    }

    &:hover,
    &:focus,
    &:active,
    &:focus-within {
      background-color: var(--ant-color-highlight);

      & .npkNavNodeActions {
        display: flex;
      }
    }

    &.ant-tree-treenode-checkbox-checked {
      background-color: var(--ant-color-highlight-weak);
      border-right: var(--ant-color-highlight-strong) 2px solid;
    }
  }

  & .ant-tree-checkbox {
    display: none;
  }
  .ant-tree-switcher.ant-tree-switcher-noop {
    display: none;
  }
  & .ant-tree-switcher {
    order: 1;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &.ant-tree-switcher_close svg {
      transform: rotate(180deg);
    }
  }
  & .ant-tree-node-content-wrapper {
    border-radius: 0;
    padding: 0 calc(var(--ant-padding-xs) / 4);
  }
}

.tags-custom-tree.ant-tree {
  & .ant-tree-treenode:not(.ant-tree-treenode-leaf-last) {
    height: var(--ant-control-height);
    margin-bottom: var(--ant-margin-sm);
  }
}

.ant-select-single .ant-select-selector {
  border-radius: 0 !important;
}
