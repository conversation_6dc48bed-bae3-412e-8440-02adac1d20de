.ag-theme-orchestrator {
  // The agGrid theme specific properties can be found in the grid-container.module.scss
  // Reserve this file for supplying only overrides don't provided by agGrid API natively.

  // cw note: this may go away, but here for now to provide column separation on pinned columns.
  .ag-header > .ag-pinned-left-header {
    border-right: var(--ag-border-color) 1px solid;
  }

  .ag-root .ag-body {
    border-top: var(--ag-border-color) 1px solid;
  }

  // widget theme alignment, better this way vs creating custom comps for all scenarios currently.
  .ag-filter {
    & .ag-input-field-input {
      min-height: var(--ant-control-height);
    }

    & .ag-checkbox-input-wrapper {
      cursor: pointer;
      &:after {
        border: var(--ant-color-border) var(--ant-line-width) solid;
      }
    }

    & .ag-filter-apply-panel .ag-filter-apply-panel-button {
      background-color: var(--ant-color-bg-elevated);
      border: var(--ant-color-border) var(--ant-line-width) solid;
      border-radius: var(--ant-border-radius);
      margin-left: var(--ant-margin-sm);
      cursor: pointer;
      // These two shouldn't be necessary, but aggrid won't inherit in
      // this nested control and their var overrides didn't work from their docs.
      font-family: var(--ant-font-family);
      font-size: var(--ant-font-size);

      &:hover,
      &:focus {
        color: var(--ant-color-primary-hover);
        border-color: var(--ant-color-primary-hover);
      }
    }
  }

  .ag-menu-option {
    cursor: pointer;
  }

  // remove duplicated right border on last header cell when resize-able
  // cw todo: go find all the overrides for sticky col head/row/last instead so re-sorting doesnt lose border on last-child instead.
  .ag-header-row
    .ag-header-cell:nth-last-child(-n + 1)
    .ag-header-cell-resize:first-child:after {
    background-color: transparent;
  }

  .grid-header-left-arrow,
  .grid-header-right-arrow {
    .ag-header-cell-text {
      width: 100%;
      text-align: center;
    }
  }

  .grid-header-left-arrow {
    .ag-header-cell-text::before,
    .ag-column-select-column-label::before {
      content: '\2190'; // unicode for left arrow
      font-size: var(--ant-font-size-lg);
      margin-right: var(--ant-margin-xs);
    }
  }

  .grid-header-right-arrow {
    .ag-header-cell-text::after,
    .ag-column-select-column-label::after {
      content: '\2192'; // unicode for right arrow
      font-size: var(--ant-font-size-lg);
      margin-left: var(--ant-margin-xs);
    }
  }

  // cw todo : investigate if these are necessary here.
  .ag-tooltip {
    max-width: 450px;
    word-break: break-word;
  }

  .ag-root .ag-body {
    border-top-color: transparent;
  }

  // custom auto height
  .ag-grid-auto-height {
    .ag-root-wrapper-body.ag-layout-normal {
      height: auto;
    }
  }

  .ag-grid-no-style {
    .ag-header {
      border-bottom: unset;
    }
    .ag-row {
      background-color: #fff;
      border-bottom: unset;
    }
    .ag-root-wrapper {
      border: unset;
    }
  }
}

// instance explicit for overcoming modals usages etc rendered outside parent elem.
// cw note: keep an eye out for collisions with other 3rd party libs
.ag-popup .ag-popup-positioned-under,
.ag-panel.ag-popup-child,
.ag-menu.ag-popup-child {
  z-index: 1201;
}

.grid-wrapper-search input {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-section-empty-container .ag-center-cols-viewport {
  min-height: 150px !important;
}
