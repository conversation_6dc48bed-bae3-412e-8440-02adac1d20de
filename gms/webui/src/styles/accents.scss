/* Elevation */
.boxShadow {
  box-shadow: var(--ant-box-shadow);
}
.boxShadowSecondary {
  box-shadow: var(--ant-box-shadow-secondary);
}
.boxShadowTertiary {
  box-shadow: var(--ant-box-shadow-tertiary);
}
/* Border Radii */
.borderRadius {
  border-radius: var(--ant-border-radius) !important;
}
/* Border default */
.border {
  border-width: 1px;
  border-style: solid;
  border-color: var(--ant-color-border);
}
.borderRadiusSmall {
  border-radius: var(--ant-border-radius-sm) !important;
}
.borderRadiusXSmall {
  border-radius: var(--ant-border-radius-xs) !important;
}
.borderRadiusLarge {
  border-radius: var(--ant-border-radius-lg) !important;
}
.borderRadius0 {
  border-radius: 0 !important;
}
/* Overlay & Underlay */
.overlay {
  background-color: var(--ant-color-bg-mask) !important;
}
.underlay {
  background-color: var(--ant-color-bg-spotlight) !important;
}
.textGradient {
  background: var(--ant-color-gradient);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
