@use 'sass:string';
// This file generates 1500+ lines of CSS utilities. Be cautious with edits.
// Design System Properties palette, each correlates to an available Design Token specified in AntD.
// Separated values since scss string manipulation can become harder to read vs usability.

$varPrefix: '--ant-';

$antd-status-palette: ('Primary', 'Success', 'Warning', 'Error', 'Info');

// Color variants available, initial left blank intentionally.
$antd-status-variants: (
  '': '',
  'Bg': '-bg',
  'BgHover': '-bg-hover',
  'Border': '-border',
  'BorderHover': '-border-hover',
  'Hover': '-hover',
  'Active': '-active',
  'TextHover': '-text-hover',
  'Text': '-text',
  'TextActive': '-text-active',
);

// Extending the antD general design palette.
$antd-extended-design-palette: (
  'Brand': 'brand',
  'White': 'white',
  'Black': 'black',
  'Accent': 'accent',
  'AccentWeak': 'accent-weak',
  'AccentXWeak': 'accent-x-weak',
  'AccentStrong': 'accent-strong',
  'AccentXStrong': 'accent-x-strong',
  'Highlight': 'highlight',
  'HighlightWeak': 'highlight-weak',
  'HighlightStrong': 'highlight-strong',
  'HighlightXStrong': 'highlight-x-strong',
  'Link': 'link',
);

// antD general text explicit color palette.
$antd-foregrounds-palette: (
  'TextBase': 'text-base',
  'Text': 'text',
  'TextSecondary': 'text-secondary',
  'TextTertiary': 'text-tertiary',
  'TextQuaternary': 'text-quaternary',
  'TextLightSolid': 'text-light-solid',
  'TextHeading': 'text-heading',
  'TextLabel': 'text-label',
  'TextDescription': 'text-description',
  'TextDisabled': 'text-disabled',
  'TextPlaceholder': 'text-placeholder',
  'TextActive': 'text-active',
);

// antD general background color palette
$antd-backgrounds-palette: (
  'BgBase': 'bg-base',
  'BgContainer': 'bg-container',
  'BgCointainerDisabled': 'bg-container-disabled',
  'BgElevated': 'bg-elevated',
  'BgLayout': 'bg-layout',
  'BgMask': 'bg-mask',
  'BgSpotlight': 'bg-spotlight',
);

// Orchestrator explicit network related colors palette
$appliance-network-palette: (
  'Lan': 'lan',
  'LanWeak': 'lan-weak',
  'Wan': 'wan',
  'WanWeak': 'wan-weak',
  'WanPayload': 'wan-payload',
  'NetRatio': 'net-ratio',
  'NetTotal': 'net-total',
  'NetCreated': 'net-created',
  'NetDeleted': 'net-deleted',
  'NetDenied': 'net-denied',
  'NetDeniedWeak': 'net-denied-weak',
  'ClientLatency': 'client-latency',
  'ServerLatency': 'server-latency',
);

// Orchestrator explicit appliance alarm color palette.
$appliance-alarm-palette: (
  'AlarmUnknown': 'unknown',
  'AlarmUnknownActive': 'unknown-active',
  'AlarmCritical': 'critical',
  'AlarmCriticalActive': 'critical-active',
  'AlarmMajor': 'major',
  'AlarmMajorActive': 'major-active',
  'AlarmMinor': 'minor',
  'AlarmMinorActive': 'minor-active',
  'AlarmWarning': 'warning',
  'AlarmWarningActive': 'warning-active',
  'AlarmOk': 'ok',
  'AlarmOkActive': 'ok-active',
  'AlarmInfo': 'info',
  'AlarmInfoActive': 'info-active',
  'AlarmCriticalBg': 'critical-bg',
);

// Generate AntD general status color & variants CSS utilities for foreground, background, & border.
// Output Sample:
// .colorPrimaryActive {
//    color: var(--ant-color-primary-active);
// }
@each $token in $antd-status-palette {
  @each $variant, $cssVar in $antd-status-variants {
    $status: string.to-lower-case(#{$token});
    .color#{$token}#{$variant} {
      color: var(#{$varPrefix}color-#{$status}#{$cssVar}) !important;
    }
    .background#{$token}#{$variant} {
      background-color: var(#{$varPrefix}color-#{$status}#{$cssVar}) !important;
    }
    .border#{$token}#{$variant} {
      border-color: var(#{$varPrefix}color-#{$status}#{$cssVar}) !important;
    }
  }
}

// Generate general antD aligned Design Token CSS utilities for foreground, background, & border..
@each $token, $cssVar in $antd-extended-design-palette {
  .color#{$token} {
    color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
  .background#{$token} {
    background-color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
  .border#{$token} {
    border-color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
}

// Generate general antD backgrounds Design Token CSS utilities
@each $token, $cssVar in $antd-backgrounds-palette {
  .background#{$token} {
    background-color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
}

// Generate general antD foreground Design Token CSS utilities
@each $token, $cssVar in $antd-foregrounds-palette {
  .color#{$token} {
    color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
}

// Generate Appliance Network Color Palette CSS utilities for foreground, background, & border..
@each $token, $cssVar in $appliance-network-palette {
  .color#{$token} {
    color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
  .background#{$token} {
    background-color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
  .border#{$token} {
    border-color: var(#{$varPrefix}color-#{$cssVar}) !important;
  }
}

// Generate Appliance Alarm Status Color Palette CSS utilities for foreground, background, & border..
@each $token, $cssVar in $appliance-alarm-palette {
  .color#{$token} {
    color: var(#{$varPrefix}alarm-#{$cssVar}) !important;
  }
  .background#{$token} {
    background-color: var(#{$varPrefix}alarm-#{$cssVar}) !important;
  }
  .border#{$token} {
    border-color: var(#{$varPrefix}alarm-#{$cssVar}) !important;
  }
}

// Generic color palette, primarily for generic charts and output that comes with AntD.
$generic-palette: (
  'Blue',
  'Geekblue',
  'Cyan',
  'Gold',
  'Green',
  'Lime',
  'Magenta',
  'Orange',
  'Purple',
  'Red',
  'Volcano',
  'Yellow'
);

// Generate generic color palette utility foreground color classes
@each $color in $generic-palette {
  .color#{$color} {
    color: var(#{$varPrefix}#{string.to-lower-case($color)}) !important;
  }
  @for $i from 1 through 10 {
    .color#{$color}#{$i} {
      color: var(#{$varPrefix}#{string.to-lower-case($color)}-#{$i}) !important;
    }
    // *Note: Can include these if found helpful later but for now left for reference.
    // .border#{$color}#{$i} {
    //   border-color: var(#{$varPrefix}#{string.to-lower-case($color)}-#{$i}) !important;
    // }
    // .background#{$color}#{$i} {
    //   background-color: var(#{$varPrefix}#{string.to-lower-case($color)}-#{$i}) !important;
    // }
  }
}

.badgeHover:hover,
.badgeFocus:focus,
.badgeAction:hover,
.badgeAction:focus {
  cursor: pointer;
  filter: brightness(0.8);
  -webkit-filter: brightness(0.8);
}

// Non generated additional global CSS utilities for providing general Status & Alarm badge type artifacts.
// General Status Badges that provide foreground & background colors accordingly.
%badgeCenter {
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  white-space: nowrap;
  padding: 0 var(--ant-padding-xs);
}

.badgePrimary {
  @extend %badgeCenter;
  color: var(--ant-color-white);
  background-color: var(--ant-color-primary);
}

.badgeInfo {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-color-info);
}

.badgeWarning {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-color-warning);
}

.badgeError {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-color-error);
}

.badgeSuccess {
  @extend %badgeCenter;
  color: var(--ant-color-white);
  background-color: var(--ant-color-success);
}

.badgeAlarmOk {
  @extend %badgeCenter;
  color: var(--ant-color-white);
  background-color: var(--ant-alarm-ok);
}

.badgeAlarmCritical {
  @extend %badgeCenter;
  color: var(--ant-color-white);
  background-color: var(--ant-alarm-critical);
}

.badgeAlarmMinor {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-alarm-minor);
}

.badgeAlarmMajor {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-alarm-major);
}

.badgeAlarmWarning {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-alarm-warning);
}

.badgeAlarmUnknown {
  @extend %badgeCenter;
  color: var(--ant-color-black);
  background-color: var(--ant-alarm-unknown);
}

.badgeAlarmDefault {
  @extend %badgeCenter;
}
