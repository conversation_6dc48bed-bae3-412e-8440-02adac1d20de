.cursorAlias {
  cursor: alias;
}
.cursorAll-scroll {
  cursor: all-scroll;
}
.cursorAuto {
  cursor: auto;
}
.cursorCell {
  cursor: cell;
}
.cursorCol-resize {
  cursor: col-resize;
}
.cursorContext-menu {
  cursor: context-menu;
}
.cursorCopy {
  cursor: copy;
}
.cursorCrosshair {
  cursor: crosshair;
}
.cursorDefault {
  cursor: default;
}
.cursorEResize {
  cursor: e-resize;
}
.cursorEwResize {
  cursor: ew-resize;
}
.cursorGrab {
  cursor: grab;
}
.cursorGrabbing {
  cursor: grabbing;
}
.cursorHelp {
  cursor: help;
}
.cursorMove {
  cursor: move;
}
.cursorNResize {
  cursor: n-resize;
}
.cursorNeResize {
  cursor: ne-resize;
}
.cursorNeswResize {
  cursor: nesw-resize;
}
.cursorNsResize {
  cursor: ns-resize;
}
.cursorNwResize {
  cursor: nw-resize;
}
.cursorNwseResize {
  cursor: nwse-resize;
}
.cursorNoDrop {
  cursor: no-drop;
}
.cursorNone {
  cursor: none;
}
.cursorNotAllowed {
  cursor: not-allowed;
}
.cursorPointer {
  cursor: pointer;
}
.cursorProgress {
  cursor: progress;
}
.cursorRowResize {
  cursor: row-resize;
}
.cursorSResize {
  cursor: s-resize;
}
.cursorSeResize {
  cursor: se-resize;
}
.cursorSwResize {
  cursor: sw-resize;
}
.cursorText {
  cursor: text;
}
/* .cursor-url {cursor: url(myBall.cursor-cur),auto;} */
.cursorWResize {
  cursor: w-resize;
}
.cursorWait {
  cursor: wait;
}
.cursorZoomIn {
  cursor: zoom-in;
}
.cursorZoomOut {
  cursor: zoom-out;
}
