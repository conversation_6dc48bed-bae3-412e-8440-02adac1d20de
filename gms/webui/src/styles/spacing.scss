/* Margins, Padding, Flex gap, etc. (SCSS) */
@use 'sass:string';
// Note: Default key empty val is intentional for defaults.
$sizes: (
  'XxSmall': '-xxs',
  'XSmall': '-xs',
  'Small': '-sm',
  'Default': '',
  'Medium': '-md',
  'Large': '-lg',
  'XLarge': '-xl',
  'XxLarge': '-xxl',
);
$sides: 'top', 'right', 'bottom', 'left';
$axii: 'x', 'y';
$varPrefix: '--ant';

// Non generated additions
.m0 {
  margin: 0 !important;
}
.p0 {
  padding: 0 !important;
}
.mAuto {
  margin: auto !important;
}
.mxAuto {
  margin-left: auto !important;
  margin-right: auto !important;
}
.myAuto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.mx0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
.my0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.px0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.py0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

// generate margins and padding (sides) utility classes.
@each $size, $suffix in $sizes {
  .m#{$size} {
    margin: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .mx#{$size} {
    margin-left: var(#{$varPrefix}-margin#{$suffix}) !important;
    margin-right: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .my#{$size} {
    margin-top: var(#{$varPrefix}-margin#{$suffix}) !important;
    margin-bottom: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .p#{$size} {
    padding: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
  .px#{$size} {
    padding-left: var(#{$varPrefix}-padding#{$suffix}) !important;
    padding-right: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
  .py#{$size} {
    padding-top: var(#{$varPrefix}-padding#{$suffix}) !important;
    padding-bottom: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
  .gap#{$size} {
    gap: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
}

// non-generated full box margin & padding default utility classes
.mDefault {
  margin: var(#{$varPrefix}-margin) !important;
}
.pDefault {
  padding: var(#{$varPrefix}-padding) !important;
}

// generate full box margin & padding removal utility classes
@each $side in $sides {
  $prefix: string.slice($side, 1, 1);
  .m#{$prefix}0 {
    margin-#{$side}: 0 !important;
  }
  .p#{$prefix}0 {
    padding-#{$side}: 0 !important;
  }
}

// generate individual box side specific spacing utility classes
@each $size, $suffix in $sizes {
  @each $side in $sides {
    $prefix: string.slice($side, 1, 1);
    .m#{$prefix}#{$size} {
      margin-#{$side}: var(#{$varPrefix}-margin#{$suffix}) !important;
    }
    .p#{$prefix}#{$size} {
      padding-#{$side}: var(#{$varPrefix}-padding#{$suffix}) !important;
    }
  }
}

// X
@each $size, $suffix in $sizes {
  .mx#{$size} {
    margin-left: var(#{$varPrefix}-margin#{$suffix}) !important;
    margin-right: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .px#{$size} {
    padding-left: var(#{$varPrefix}-padding#{$suffix}) !important;
    padding-right: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
}

// Y
@each $size, $suffix in $sizes {
  .my#{$size} {
    margin-top: var(#{$varPrefix}-margin#{$suffix}) !important;
    margin-bottom: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .py#{$size} {
    padding-top: var(#{$varPrefix}-padding#{$suffix}) !important;
    padding-bottom: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
}

// X (left and right only)
@each $size, $suffix in $sizes {
  .mxLeft#{$size} {
    margin-left: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .mxRight#{$size} {
    margin-right: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .pxLeft#{$size} {
    padding-left: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
  .pxRight#{$size} {
    padding-right: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
}

// Y (top and bottom only)
@each $size, $suffix in $sizes {
  .mxTop#{$size} {
    margin-top: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .mxBottom#{$size} {
    margin-bottom: var(#{$varPrefix}-margin#{$suffix}) !important;
  }
  .pxTop#{$size} {
    padding-top: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
  .pxBottom#{$size} {
    padding-bottom: var(#{$varPrefix}-padding#{$suffix}) !important;
  }
}

// Auto margins
@each $side in $sides {
  $prefix: string.slice($side, 1, 1);
  .m#{$prefix}Auto {
    margin-#{$side}: auto !important;
  }
}
