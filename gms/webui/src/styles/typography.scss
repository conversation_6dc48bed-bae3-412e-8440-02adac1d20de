// Generate general font sizing classes
$base-font-sizes: (
  'XSmall': 'xs',
  'Small': 'sm',
  'Medium': 'md',
  'Large': 'lg',
  'XLarge': 'xl',
);

@each $size, $suffix in $base-font-sizes {
  .fontSize#{$size} {
    font-size: var(--ant-font-size-#{$suffix}) !important;
  }
}

// Headings
.fontSizeHeading1 {
  font-size: var(--ant-font-size-heading-1);
}
.fontSizeHeading2 {
  font-size: var(--ant-font-size-heading-2);
}
.fontSizeHeading3 {
  font-size: var(--ant-font-size-heading-3);
}

// Variants
.fontItalic {
  font-style: italic !important;
}
.fontNormal {
  font-style: normal !important;
  font-weight: 400 !important;
}
.fontLight {
  font-weight: 300 !important;
}
.fontLighter {
  font-weight: lighter !important;
}
.fontBold {
  font-weight: 600 !important;
}
.fontBolder {
  font-weight: bolder !important;
}
