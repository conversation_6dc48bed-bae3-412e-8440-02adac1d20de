import { useEffect } from 'react';
import PropTypes from 'prop-types';

import { validateHookParams } from '@common/utils';
import { queryClient } from '@config/query/query.config.js';

function useQueryCacheClear(params) {
  validateHookParams(useQueryCacheClear, params);

  const { queryKey, exact = false, enabled = true } = params;

  useEffect(() => {
    return () => {
      if (enabled && queryKey) {
        queryClient.removeQueries({
          queryKey,
          exact,
        });
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}

useQueryCacheClear.propTypes = {
  queryKey: PropTypes.array.isRequired,
  exact: PropTypes.bool,
  enabled: PropTypes.bool,
};

export { useQueryCacheClear };
