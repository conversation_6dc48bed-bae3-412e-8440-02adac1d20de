import { useQueries } from '@tanstack/react-query';
import { capitalize } from 'radash';
import { api } from '@sdk';

import { collator } from '@common/utils';

function useInternetDb({
  fetchSaasOrOrgNames,
  fetchSubAttrTemplate,
  fetchTopSites,
  fetchCountries,
  fetchUserAdf,
}) {
  const internetDbQueries = useQueries({
    queries: [
      {
        queryKey: ['common:getSaasOrOrgNames'],
        queryFn: ({ signal }) =>
          api.spPortal.internetDb.getSaasOrg({
            params: {
              pattern: '',
            },
            signal,
          }),
        select: (response) => response.data,
        enabled: fetchSaasOrOrgNames,
      },
      {
        queryKey: ['common:getSubAttributesTemplate'],
        queryFn: api.spPortal.internetDb.getSubAttributesTemplate,
        select: (response) => response.data,
        enabled: fetchSubAttrTemplate,
      },
      {
        queryKey: ['common:getTopSites'],
        queryFn: api.spPortal.getTopSites,
        select: (response) => response.data,
        enabled: fetchTopSites,
      },
      {
        queryKey: ['common:getCountries'],
        queryFn: api.spPortal.internetDb.getCountries,
        select: (response) => response.data,
        enabled: fetchCountries,
      },
      {
        queryKey: ['common:getUserAdf'],
        queryFn: api.spPortal.internetDb.getUserAdf,
        select: (response) => response.data,
        enabled: fetchUserAdf,
      },
    ],
  });

  const countryNames = Object.keys(internetDbQueries[3].data || {})
    .filter((c) => c !== '-')
    .map(capitalize);

  const sortedCountryNames = countryNames.sort((a, b) =>
    collator.compare(a.name, b.name),
  );

  return {
    saasOrOrgNames: internetDbQueries[0].data,
    subAttributesTemplate: internetDbQueries[1].data,
    topSites: internetDbQueries[2].data?.map((site) => `*${site}`),
    countries: sortedCountryNames,
    userAdf: internetDbQueries[4].data,
    isLoading: internetDbQueries.some((i) => i.isLoading),
  };
}

export { useInternetDb };
