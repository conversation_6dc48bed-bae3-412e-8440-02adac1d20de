import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { GridDelete } from '@common/components/grid-delete/grid-delete';
import { GridEdit } from '@common/components/grid-edit/grid-edit';
import { GridDetails } from '@common/components/grid-details/grid-details';
import { GridButton } from '@common/components/grid-button/grid-button';
import { GridActions } from '@common/components';

import { defaultActionColumnProps } from './use-grid-actions.constants';

/**
 * Hook for managing the grid actions
 *
 * @param {Object} options
 * @return {Object} {state, columns, utils}
 */
function useGridActions({ gridRef = {} }) {
  const [updatedRows, setUpdatedRows] = useState(null);
  const { t } = useTranslation();
  const getUpdatedRowData = () => {
    const rowsAfterUpdate = [];
    gridRef.current?.api?.forEachNode((node) =>
      rowsAfterUpdate.push(node.data),
    );
    return rowsAfterUpdate;
  };

  const getRowsUsingNepk = (nePk) => {
    const rows = {};
    gridRef.current?.api?.forEachNode((node) => {
      if (node.data.nePk === nePk && node.data.self) {
        let data = { ...node.data };
        delete data.applianceName;
        delete data.nePk;
        rows[data.self] = data;
      }
    });
    return rows;
  };

  const getRowsArrayUsingNepk = (nePk) => {
    const rows = [];
    gridRef.current?.api?.forEachNode((node) => {
      if (node.data.nePk === nePk) {
        let data = { ...node.data };
        delete data.applianceName;
        rows.push(data); // Push the data object into the array
      }
    });

    return rows;
  };

  const getRowsArrayWithUpdatedValueUsingNepk = (rid, updatedRowData, nePk) => {
    const rowsArray = [];

    gridRef.current?.api?.forEachNode((node) => {
      if (node.data.nePk === nePk) {
        let data = { ...node.data };
        if (node.id === rid) {
          delete updatedRowData.applianceName;
          rowsArray.push(updatedRowData);
        } else {
          delete data.applianceName;
          rowsArray.push(data);
        }
      }
    });

    return rowsArray;
  };

  const getRowsWithUpdatedValueUsingNepk = (rid, updatedRowData, nePk) => {
    const rows = {};
    gridRef.current?.api?.forEachNode((node) => {
      if (node.data.nePk === nePk && node.data.self) {
        let data = { ...node.data };
        if (node.id === rid) {
          delete updatedRowData.applianceName;
          delete updatedRowData.nePk;
          rows[updatedRowData.self] = updatedRowData;
        } else {
          delete data.applianceName;
          delete data.nePk;
          rows[data.self] = data;
        }
      }
    });
    return rows;
  };

  const getRowsArrayAfterDelete = (rid) => {
    const rows = [];

    gridRef.current?.api?.forEachNode((node) => {
      let newData = { ...node.data };

      if (node.id !== rid) {
        delete newData.applianceName;
        delete newData.nePk;
        rows.push(newData);
      }
    });

    return rows;
  };

  const getRowsAfterDelete = (rid, nePk) => {
    const rows = {};
    gridRef.current?.api?.forEachNode((node) => {
      if (node.data.nePk === nePk && node.data.self) {
        let data = { ...node.data };
        if (node.id !== rid) {
          delete data.applianceName;
          delete data.nePk;
          rows[data.self] = data;
        }
      }
    });
    return rows;
  };

  const handleDeleteMultipleRows = () => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    selectedNodes.forEach((node) => {
      gridRef?.current?.api?.applyTransaction({ remove: [node.data] });
    });
    setUpdatedRows(getUpdatedRowData());
  };

  const handleDeleteRow = (node) => {
    gridRef?.current?.api?.applyTransaction({ remove: [node.data] });
    setUpdatedRows(getUpdatedRowData());
  };

  const handleAddRow = (newRow, index = 0) => {
    gridRef?.current?.api?.applyTransaction({
      add: [newRow],
      addIndex: index,
    });
    setUpdatedRows(getUpdatedRowData());
  };

  const handleResetData = (data) => {
    gridRef?.current?.api?.setGridOption('rowData', [...data]);
    setUpdatedRows(getUpdatedRowData());
  };

  const handleUpdateRow = (updatedRow) => {
    gridRef?.current?.api?.applyTransaction({ update: [updatedRow] });
    setUpdatedRows(getUpdatedRowData());
  };

  const applianceColumn = {
    field: 'applianceName',
    headerName: t('appliance'),
    minWidth: 100,
    flex: 1,
    filter: 'agTextColumnFilter',
    sort: 'asc',
  };

  const deleteButtonColumn = {
    ...defaultActionColumnProps,
    cellRenderer: GridDelete,
    pinned: 'right', // keeping it always pinned to right by default
    lockPosition: 'right', // keeping it always right positioned to avoid dragging on other field drag
  };

  const editButtonColumn = {
    headerName: t('edit'),
    cellRenderer: GridEdit,
    suppressMovable: true,
    lockPosition: 'left',
    pinned: 'left',
    ...defaultActionColumnProps,
  };

  const actionsButtonColumn = {
    headerName: t('actions'),
    cellRenderer: GridActions,
    suppressMovable: true,
    lockPosition: 'left',
    pinned: 'left',
    ...defaultActionColumnProps,
  };

  const detailsButtonColumn = {
    cellRenderer: GridDetails,
    ...defaultActionColumnProps,
  };

  const gridButtonColumn = {
    ...defaultActionColumnProps,
    resizable: true,
    width: 80,
    cellRenderer: GridButton,
  };

  return {
    state: {
      updatedRows,
    },
    columns: {
      deleteButtonColumn,
      editButtonColumn,
      detailsButtonColumn,
      gridButtonColumn,
      applianceColumn,
      actionsButtonColumn,
    },
    utils: {
      onUpdateRow: handleUpdateRow,
      onDeleteRow: handleDeleteRow,
      onAddRow: handleAddRow,
      getUpdatedRowData,
      handleResetData,
      handleDeleteMultipleRows,
      getRowsUsingNepk,
      getRowsWithUpdatedValueUsingNepk,
      getRowsAfterDelete,
      getRowsArrayAfterDelete,
      getRowsArrayUsingNepk,
      getRowsArrayWithUpdatedValueUsingNepk,
    },
  };
}

export { useGridActions };
