import { useQuery } from '@tanstack/react-query';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '@sdk';

import { queryClient } from '@config/query/query.config.js';
import { useDispatch } from 'react-redux';

const queryKey = ['common:logout'];

const useLogout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { refetch, isSuccess } = useQuery({
    queryKey,
    enabled: false,
    queryFn: api.auth.logout,
  });

  const logout = () => {
    refetch();
  };

  /**
   * navigates to login WITHOUT calling log out API
   */
  const navToLogin = useCallback(() => {
    navigate('/login');
    queryClient.resetQueries({
      queryKey,
    });
    // clear redux states
    dispatch({ type: 'logout' });
  }, [dispatch]);

  useEffect(() => {
    if (isSuccess) {
      navToLogin();
    }
  }, [isSuccess, navigate]);

  return {
    logout,
    navToLogin,
  };
};

export { useLogout };
