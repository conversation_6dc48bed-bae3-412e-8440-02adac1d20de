import { useEffect, useRef } from 'react';

/**
 * Hook to run a function every interval
 * @param {number} interval
 * @param {func} callback
 */
function useInterval(interval, callback) {
  const intervalRef = useRef(null);

  const clear = () => {
    clearInterval(intervalRef.current);
    intervalRef.current = null;
  };

  useEffect(() => {
    clear();
    intervalRef.current = setInterval(callback, interval);
    return clear;
  }, [interval]);
}

export { useInterval };
