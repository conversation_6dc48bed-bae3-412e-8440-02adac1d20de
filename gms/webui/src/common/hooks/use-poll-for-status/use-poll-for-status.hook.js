import { useQuery } from '@tanstack/react-query';
import { useCallback, useEffect } from 'react';
import { api } from '@sdk';

import { getCompletionStatus } from '@common/utils/action-manager.utils';

/**
 * Hook for managing poll status
 *
 * @param {object}
 * @return {Object} {onProgressUpdate, fetchPollForStatus, isFetching}
 */

function usePollForStatus({
  enabled = false,
  key,
  delay,
  finishCallback,
  updateCallback,
}) {
  const { data, isSuccess, refetch, isFetching } = useQuery({
    queryKey: ['common:get-action-status', key],
    queryFn: ({ signal }) =>
      api.action.getStatus({
        params: { key },
        signal,
      }),
    select: ({ data }) => data,
    enabled,
  });
  const setupProgressUpdate = useCallback(
    (addDelay) => {
      if (addDelay) {
        setTimeout(refetch, delay);
      } else {
        refetch();
      }
    },
    [delay, refetch],
  );

  useEffect(() => {
    if (isSuccess && data && !isFetching) {
      const percentComplete = getCompletionStatus(data);
      if (updateCallback) {
        updateCallback(data);
      }
      if (percentComplete === 100) {
        finishCallback(data);
      } else {
        setupProgressUpdate(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isSuccess, refetch, delay, setupProgressUpdate, isFetching]);

  return {
    onProgressUpdate: setupProgressUpdate,
    fetchPollForStatus: refetch,
    isFetching,
  };
}

export { usePollForStatus };
