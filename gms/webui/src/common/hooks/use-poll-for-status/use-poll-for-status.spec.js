import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook } from '@test-utils';
import { api } from '@sdk';

import { usePollForStatus } from './use-poll-for-status.hook';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: usePollForStatus', async () => {
  it('calls get api', async () => {
    const axiosGet = vi.spyOn(api.action, 'getStatus');

    const { result } = renderHook(() =>
      usePollForStatus({
        enabled: false,
        key: 'key',
        delay: 500,
        finishCallback: vi.fn(),
      }),
    );
    await result.current.onProgressUpdate();

    expect(axiosGet).toHaveBeenCalled();
  });
});
