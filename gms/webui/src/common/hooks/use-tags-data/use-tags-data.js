import { pollInterval } from '@common/constants';
import { useQueries } from '@tanstack/react-query';
import { api } from '@sdk';

const useTagsData = () => {
  const pollingOptions = {
    enabled: true,
    refetchInterval: pollInterval.SEC_30,
    retry: true,
  };
  const tagsData = useQueries({
    queries: [
      {
        queryKey: ['all-appliances'],
        queryFn: api.appliance.getAppliances,
        ...pollingOptions,
      },
      {
        queryKey: ['all-groups'],
        queryFn: api.gms.getGroups,
        ...pollingOptions,
      },
      {
        queryKey: ['appliance-regions'],
        queryFn: api.regions.getRegionAppliances,
        ...pollingOptions,
      },
      {
        queryKey: ['all-regions'],
        queryFn: api.regions.get,
        ...pollingOptions,
      },
      {
        queryKey: ['interface-labels'],
        queryFn: api.gms.getInterfaceLabels,
        ...pollingOptions,
      },
      {
        queryKey: ['overlay-config'],
        queryFn: api.gms.overlays.config.get,
        ...pollingOptions,
      },
      {
        queryKey: ['appliance-hapeer'],
        queryFn: api.appliance.getHaPeerMeta,
        ...pollingOptions,
      },
      {
        queryKey: ['appliance-zonelist'],
        queryFn: api.appliance.getZoneListMeta,
        ...pollingOptions,
      },
      {
        queryKey: ['appliance-interface-list'],
        queryFn: api.appliance.getInterfaceMeta,
        ...pollingOptions,
      },
      {
        queryKey: ['overlay-association'],
        queryFn: api.gms.getOverlayAssociation,
        ...pollingOptions,
      },
      {
        queryKey: ['maintenance-mode-appliances'],
        queryFn: api.maintenance.get,
      },
      {
        queryKey: ['schedule-timezone'],
        queryFn: api.gms.getScheduleTimezone,
        ...pollingOptions,
      },
    ],
    combine: (results) => {
      return {
        isSuccess: results.every((result) => result.isSuccess),
        appliances: results[0]?.data?.data,
        isApplianceSuccess: results[0]?.isSuccess,
        groupsData: results[1]?.data?.data,
        isGroupsSuccess: results[1]?.isSuccess,
        applianceRegions: results[2]?.data?.data,
        isApplianceRegionsSuccess: results[2]?.isSuccess,
        allRegions: results[3]?.data?.data,
        isRegionsSuccess: results[3]?.isSuccess,
        interfaceLabels: results[4]?.data?.data,
        overlayConfig: results[5]?.data?.data,
        applianceHaPeer: results[6]?.data?.data,
        isApplianceHaPeerSuccess: results[6]?.isSuccess,
        applianceZoneList: results[7]?.data?.data,
        applianceInterfaceList: results[8]?.data?.data,
        overlayAssociation: results[9]?.data?.data,
        scheduleTimezone: results[12]?.data?.data,
      };
    },
  });
  return tagsData;
};

export { useTagsData };
