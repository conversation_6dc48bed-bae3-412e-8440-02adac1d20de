import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';
import { t } from 'i18next';

function useOverlayOptions({ hasAllOverlays, hasAllUnderlays }) {
  const { data: overlays, isLoading } = useQuery({
    queryKey: ['common:get-overlay-options'],
    queryFn: api.gms.overlays.config.get,
    select: ({ data }) => data,
  });

  if (overlays && !isLoading) {
    return [
      ...(hasAllOverlays ? [{ label: t('allOverlays'), value: 'all' }] : []),
      ...overlays.map((item) => ({ label: item.name, value: item.id })),
      ...(hasAllUnderlays ? [{ label: t('allUnderlays'), value: '0' }] : []),
    ];
  }
  return [{ label: t('allOverlays'), value: 'all' }];
}

export { useOverlayOptions };
