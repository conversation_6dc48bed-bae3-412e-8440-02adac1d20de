import { useQueries } from '@tanstack/react-query';
import { api } from '@sdk';

import { t } from 'i18next';
import { queryCombineDefaultFn } from '@common/utils';

/**
 * Hook for managing VRF status
 *
 * @param {object}
 * @return {Object}
 */

function useVrfConfig({
  vrfEnabled,
  vrfSegments,
  vrfZonesMap,
  vrfSegmentZonesMap,
}) {
  const {
    enableConfig,
    invalidVrfTxt,
    vrfSegmentZonesMapData,
    vrfZonesMapData,
    segmentsData,
    isLoading,
    isLoadingCount,
    isError,
    lastRefreshTime,
    refetchEnableConfig,
    refetchSegments,
    refetchVrfZonesMap,
    refetchVrfSegmentZonesMap,
  } = useQueries({
    queries: [
      {
        queryKey: ['common:vrf-config:enable'],
        queryFn: api.vrf.getConfigEnable,
        select: (response) => response.data,
        enabled: Boolean(vrfEnabled),
      },
      {
        queryKey: ['common:vrf-config:segments'],
        queryFn: api.vrf.getConfigSegments,
        select: (response) => response.data,
        enabled: Boolean(vrfSegments),
      },
      {
        queryKey: ['common:vrf-config:vrf-zones-map'],
        queryFn: api.zones.getVrfZoneMap,
        select: (response) => response.data,
        enabled: Boolean(vrfZonesMap),
      },
      {
        queryKey: ['common:vrf-config:vrf-segment-zones-map'],
        queryFn: api.zones.getVrfSegmentZoneMap,
        select: (response) => response.data,
        enabled: Boolean(vrfSegmentZonesMap),
      },
    ],

    combine: (results) => {
      const queryCombineData = queryCombineDefaultFn(results);
      return {
        enableConfig: results[0].data?.enable,
        vrfZonesMapData: results[2].data,
        vrfSegmentZonesMapData: results[3].data,
        segmentsData: results[1].data,
        invalidVrfTxt: t('deleted'),
        isLoading: results.some((item) => item.isLoading),
        isError: results.some((item) => item.isError),
        refetchEnableConfig: results[0].refetch,
        refetchSegments: results[1].refetch,
        refetchVrfZonesMap: results[2].refetch,
        refetchVrfSegmentZonesMap: results[3].refetch,
        lastRefreshTime: queryCombineData.lastRefreshTime,
        isLoadingCount: queryCombineData.isLoadingCount,
      };
    },
  });

  return {
    enableConfig,
    invalidVrfTxt,
    vrfSegmentZonesMap: vrfSegmentZonesMapData,
    vrfZonesMap: vrfZonesMapData,
    segments: segmentsData,
    isLoading,
    isError,
    refetchEnableConfig,
    refetchSegments,
    refetchVrfZonesMap,
    refetchVrfSegmentZonesMap,
    isLoadingCount,
    lastRefreshTime,
  };
}

export { useVrfConfig };
