import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook } from '@test-utils';
import { api } from '@sdk';

import { useVrfConfig } from './use-vrf-config.hook';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useVrfConfig', async () => {
  it('calls get api', async () => {
    const getConfigEnable = vi.spyOn(api.vrf, 'getConfigEnable');
    const getConfigSegments = vi.spyOn(api.vrf, 'getConfigSegments');
    const getVrfZoneMap = vi.spyOn(api.zones, 'getVrfZoneMap');
    const getVrfSegmentZoneMap = vi.spyOn(api.zones, 'getVrfSegmentZoneMap');

    renderHook(() =>
      useVrfConfig({
        vrfEnabled: true,
        vrfSegments: true,
        vrfZonesMap: true,
        vrfSegmentZonesMap: true,
      }),
    );

    expect(getConfigEnable).toHaveBeenCalled();
    expect(getConfigSegments).toHaveBeenCalled();
    expect(getVrfZoneMap).toHaveBeenCalled();
    expect(getVrfSegmentZoneMap).toHaveBeenCalled();
  });
});
