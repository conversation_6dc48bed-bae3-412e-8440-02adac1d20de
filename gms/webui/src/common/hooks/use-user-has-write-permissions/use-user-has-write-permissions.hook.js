import { useSelector } from 'react-redux';

function useUserHasWritePermissions(menuId) {
  const authData = useSelector((state) => state.auth.permissions);

  if (authData?.menus) {
    if (!menuId) throw new Error('Requires menuId to check for RBAC user');

    const menuPermission = authData?.menus[menuId];

    if (menuPermission) {
      return menuPermission.read && menuPermission.write;
    } else {
      return false;
    }
  }

  return authData?.read && authData?.write;
}

export { useUserHasWritePermissions };
