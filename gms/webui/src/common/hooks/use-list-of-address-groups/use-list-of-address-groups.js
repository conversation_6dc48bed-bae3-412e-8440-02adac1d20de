import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

function useListOfAddressGroups() {
  const { data, isError } = useQuery({
    queryKey: ['common:list-of-address-groups'],
    queryFn: api.ipObjects.getAddressGroups,
    select: (resp) => {
      const result = resp.data || {};
      return {
        result: result,
        addressGroups: result.map((item) => ({
          label: item.name,
          value: item.name,
        })),
      };
    },
  });
  return {
    listOfAddressGroups: data,
    isError,
  };
}

export { useListOfAddressGroups };
