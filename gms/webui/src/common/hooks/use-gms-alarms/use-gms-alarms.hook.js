import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { api } from '@sdk';

function useGmsAlarms() {
  const params = useMemo(
    () => ({
      view: 'active',
      maxAlarms: 100000,
    }),
    [],
  );

  const { data: gmsAlarms } = useQuery({
    queryKey: ['gms-alarms', { params }],
    queryFn: () => api.alarm.getGmsAlarms({ params }),
  });

  return gmsAlarms;
}

export { useGmsAlarms };
