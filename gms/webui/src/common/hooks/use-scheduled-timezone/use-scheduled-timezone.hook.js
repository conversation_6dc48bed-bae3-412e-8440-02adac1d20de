import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

import { pollInterval } from '@common/constants';

function useScheduledTimezone() {
  const { data: scheduledTimeZone } = useQuery({
    queryKey: ['common:scheduled-timezone'],
    refetchInterval: pollInterval.SEC_30,
    queryFn: api.gms.scheduleTimezone.get,
    select: (resp) => {
      const result = resp.data || {};
      return result.defaultTimezone;
    },
  });
  return scheduledTimeZone;
}

export { useScheduledTimezone };
