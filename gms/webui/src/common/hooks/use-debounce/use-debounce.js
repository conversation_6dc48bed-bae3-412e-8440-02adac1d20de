import { useCallback, useEffect, useRef } from 'react';

/**
 * Callback function to be executed after provided / default time delay.
 *
 * @callback debounceCallback
 * @param {...*} args - Arguments of the callback function
 */

/**
 * `useDebounce` creates a debounced version of a function. Useful for delaying a function call,
 * like for input field validation or API calls in response to user input.
 *
 * @param {debounceCallback} callback - The function to debounce.
 * @param {number} delay - The number of milliseconds to delay the function call.
 * @param {Object} options - Optional settings including `maxWait`, `leading`, and `trailing` execution.
 * @param {number} options.maxWait - Maximum amount of time to wait
 * @param {boolean} options.leading - Consider the leading invocation
 * @param {boolean} options.trailing - Consider the trailing invocation
 * @return - An array containing the debounced function and a `cancel` function to cancel the debounce.
 */

function useDebounce(callback, delay = 500, options = {}) {
  const { maxWait, leading = false, trailing = true } = options;
  const timerIdRef = useRef(null);
  const maxTimerIdRef = useRef(null);
  const lastCallTimeRef = useRef(null);
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    return () => {
      if (timerIdRef.current) clearTimeout(timerIdRef.current);
      if (maxTimerIdRef.current) clearTimeout(maxTimerIdRef.current);
    };
  }, []);

  const debouncedFunction = useCallback(
    (...args) => {
      const now = Date.now();
      const isLeading = leading && lastCallTimeRef.current === null;

      const invokeCallback = () => {
        lastCallTimeRef.current = now;
        timerIdRef.current = null;
        maxTimerIdRef.current = null;
        callbackRef.current(...args);
      };

      if (isLeading) {
        invokeCallback();
        return;
      }

      if (timerIdRef.current) clearTimeout(timerIdRef.current);

      if (maxWait && !maxTimerIdRef.current && trailing) {
        maxTimerIdRef.current = setTimeout(invokeCallback, maxWait);
      }

      timerIdRef.current = setTimeout(invokeCallback, delay);
    },
    [delay, leading, trailing, maxWait],
  );

  const cancel = useCallback(() => {
    if (timerIdRef.current) clearTimeout(timerIdRef.current);
    if (maxTimerIdRef.current) clearTimeout(maxTimerIdRef.current);
    timerIdRef.current = null;
    maxTimerIdRef.current = null;
    lastCallTimeRef.current = null;
  }, []);

  return [debouncedFunction, cancel];
}

export { useDebounce };
