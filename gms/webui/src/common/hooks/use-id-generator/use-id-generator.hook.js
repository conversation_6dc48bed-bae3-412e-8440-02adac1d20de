function useIdGenerator(key, action) {
  const keywords = [
    'add',
    'modal',
    'grid',
    'form',
    'search',
    'link',
    'refresh',
    'download',
    'help',
    'section',
    'exportToExcel',
    'checkbox',
    'deleteModal',
    'radioGroup',
    'descriptions',
    'dropdown',
    'checkableTags',
    'button',
  ];

  const generatedIds = keywords.reduce((accum, keyword) => {
    accum[keyword] = `${key}:${keyword}`;
    return accum;
  }, {});

  if (action) {
    generatedIds[action] = `${key}:${action}`;
  }

  return generatedIds;
}

export { useIdGenerator };
