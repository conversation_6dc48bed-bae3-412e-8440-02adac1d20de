import { useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { omit } from 'radash';

import { commonStrings } from '@common/constants';
import {
  validateHookParams,
  setLocalStorageItem,
  getLocalStorageItem,
  getGridStateKey,
  handleGridColSelectionState,
  handleGridEmptySpace,
} from '@common/utils';

import { GRID_STATE_EXCLUDED_KEYS } from './use-grid.constants';
import { useQueryCacheClear } from '../use-query-cache-clear/use-query-cache-clear.hook';

const { HPE_ORCHESTRATOR } = commonStrings;

/**
 * Hook for managing the grid
 *
 * @param {Object} options
 * @return {Object} {defaultHandlers, defaultProps, state}
 */
function useGrid(params) {
  validateHookParams(useGrid, params);

  const { gridRef, key, keepGridState = true, keepQueryCache = false } = params;
  const [filteredRowCount, setFilteredRowCount] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const gridStateKey = getGridStateKey(key);

  useQueryCacheClear({ queryKey: [key], enabled: !keepQueryCache });

  const initialState = useMemo(() => {
    const state = getLocalStorageItem(gridStateKey);

    return state ? JSON.parse(state) : {};
  }, [gridStateKey]);

  const defaultProps = useMemo(() => {
    return {
      initialState,
      columnMenu: 'new',
      wrapHeaderText: true,
      autoHeaderHeight: true,
      tooltipShowDelay: 1000,
      suppressMenuHide: false,
      tooltipInteraction: true,
      getContextMenuItems: () => [],
      localeText: {
        noRowsToShow: 'No Data Available',
      },
      popupParent: document.getElementById('app-layout'),
      defaultExcelExportParams: {
        exportedRows: 'all',
        author: HPE_ORCHESTRATOR,
      },
      defaultColDef: {
        suppressHeaderFilterButton: true,
      },
      loading: false,
      suppressRowDeselection: true,
    };
  }, [initialState]);

  const handleFilterChanged = useCallback(() => {
    const searchValue = gridRef.current?.api.getQuickFilter();
    const filterValue = gridRef.current?.api.getFilterModel();
    if (!searchValue && !filterValue) {
      setFilteredRowCount(null);
      return;
    }
    const displayedRowCount = gridRef.current?.api.getDisplayedRowCount();
    setFilteredRowCount(displayedRowCount);
    if (displayedRowCount) {
      gridRef.current.api.hideOverlay();
    } else {
      gridRef.current.api.showNoRowsOverlay();
    }
  }, [gridRef]);

  const handleGridState = useCallback(
    ({ state }) => {
      setLocalStorageItem(gridStateKey, omit(state, GRID_STATE_EXCLUDED_KEYS));
    },
    [gridStateKey],
  );

  const handleSelectionChange = useCallback(() => {
    setSelectedRows(gridRef.current?.api.getSelectedRows());
  }, [gridRef]);

  const defaultHandlers = useMemo(() => {
    const handlers = {
      onFilterChanged: handleFilterChanged,
      onSelectionChanged: handleSelectionChange,
    };

    if (keepGridState) {
      handlers.onGridStateChange = handleGridState;
      handlers.onStateUpdated = handleGridState;
      handlers.onColumnResized = handleGridEmptySpace;

      // Requirement: Maintain column selection across tab changes and refresh
      // AG Grid is not handling properly with initialState. It is a workaround.
      handlers.onGridSizeChanged = (event) => {
        handleGridColSelectionState(event, gridStateKey);
      };
    }

    return handlers;
  }, [
    handleFilterChanged,
    handleGridState,
    handleSelectionChange,
    keepGridState,
    gridStateKey,
  ]);

  return {
    defaultProps,
    defaultHandlers,
    state: {
      filteredRowCount,
      selectedRows,
    },
  };
}

useGrid.propTypes = {
  gridRef: PropTypes.object.isRequired,
  key: PropTypes.string.isRequired,
};

export { useGrid };
