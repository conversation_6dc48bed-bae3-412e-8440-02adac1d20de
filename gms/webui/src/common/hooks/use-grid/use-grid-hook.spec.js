import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';

import { useGrid } from './use-grid.hook';

const mockGridRef = {
  current: {
    api: {
      getDisplayedRowCount: vi.fn(),
      hideOverlay: vi.fn(),
      showNoRowsOverlay: vi.fn(),
      getQuickFilter: vi.fn(),
      getFilterModel: vi.fn(),
    },
  },
};

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useGrid', async () => {
  it('returns default props, default handlers, default state and default formatters', () => {
    const { result } = renderHook(() => useGrid({ gridRef: mockGridRef }));

    expect(result.current).toHaveProperty('defaultProps');
    expect(result.current).toHaveProperty('defaultHandlers');
    expect(result.current).toHaveProperty('state');
  });

  it('returns updated filtered row count when onFilterChanged is called', () => {
    const { result } = renderHook(() => useGrid({ gridRef: mockGridRef }));

    act(() => {
      mockGridRef.current.api.getQuickFilter.mockReturnValue('searchVal');
      mockGridRef.current.api.getDisplayedRowCount.mockReturnValue(5);
      result.current.defaultHandlers.onFilterChanged();
    });

    expect(mockGridRef.current.api.getDisplayedRowCount).toHaveBeenCalled();
    expect(result.current.state.filteredRowCount).toBe(5);
    expect(mockGridRef.current.api.hideOverlay).toHaveBeenCalled();
  });

  it('returns filtered row count as 0 and calls showNoRowsOverlay', () => {
    const { result } = renderHook(() => useGrid({ gridRef: mockGridRef }));

    act(() => {
      mockGridRef.current.api.getQuickFilter.mockReturnValue('searchVal');
      mockGridRef.current.api.getDisplayedRowCount.mockReturnValue(0);
      result.current.defaultHandlers.onFilterChanged();
    });

    expect(mockGridRef.current.api.getDisplayedRowCount).toHaveBeenCalled();
    expect(result.current.state.filteredRowCount).toBe(0);
    expect(mockGridRef.current.api.showNoRowsOverlay).toHaveBeenCalled();
  });

  it('returns filtered row count as null when there is no search text', () => {
    const { result } = renderHook(() => useGrid({ gridRef: mockGridRef }));

    act(() => {
      mockGridRef.current.api.getQuickFilter.mockReturnValue('');
      result.current.defaultHandlers.onFilterChanged();
    });

    expect(result.current.state.filteredRowCount).toBe(null);
  });
});
