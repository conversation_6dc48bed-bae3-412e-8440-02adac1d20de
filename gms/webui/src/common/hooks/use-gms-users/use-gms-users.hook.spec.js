import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';
import { api } from '@sdk';

import { useGmsUsers } from '@common/hooks/use-gms-users/use-gms-users.hook';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useGmsUsers', () => {
  it('returns user information', async () => {
    const axiosGet = vi.spyOn(api.users, 'get');

    await act(() => {
      renderHook(() => useGmsUsers());
    });

    expect(axiosGet).toHaveBeenCalled();
  });
});
