import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { getLocalStorageItem, setLocalStorageItem } from '@common/utils';

import { useImmer } from '../use-immer/use-immer.hook.js';

const usePersistState = (key, initialState) => {
  const storedState = getLocalStorageItem(key);

  const [state, setState] = useImmer(
    storedState ? JSON.parse(storedState) : initialState,
  );

  useEffect(() => {
    setLocalStorageItem(key, state);
  }, [key, state]);

  return [state, setState];
};

usePersistState.propTypes = {
  key: PropTypes.string.isRequired,
};

export { usePersistState };
