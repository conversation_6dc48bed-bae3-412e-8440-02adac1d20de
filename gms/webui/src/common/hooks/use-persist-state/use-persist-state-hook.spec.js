import { describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';

import { usePersistState } from './use-persist-state.hook.js';

const localStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorage,
});

describe('Common Hook: usePersistState', async () => {
  it('returns default state and function to update the state', () => {
    const {
      result: {
        current: [state, setState],
      },
    } = renderHook(() => usePersistState('test-key', 1));

    expect(state).toBe(1);
    expect(typeof setState).toBe('function');
    expect(localStorage.getItem).toBeCalledWith('WEBUI_test-key');
  });

  it('updates state value and saves in localstorage on setFunction call - primitive', () => {
    const { result } = renderHook(() => usePersistState('test-key', 1));

    expect(result.current[0]).toBe(1);
    expect(typeof result.current[1]).toBe('function');

    act(() => {
      result.current[1](2);
    });

    expect(result.current[0]).toBe(2);
    expect(localStorage.setItem).toBeCalledWith('WEBUI_test-key', 2);
  });

  it('updates state value and saves in localstorage on setFunction call - object', () => {
    const { result } = renderHook(() =>
      usePersistState('test-key', { value: 1 }),
    );

    act(() => {
      result.current[1]((draft) => {
        draft.value = 2;
      });
    });

    expect(result.current[0]).toStrictEqual({ value: 2 });
    expect(localStorage.setItem).toBeCalledWith(
      'WEBUI_test-key',
      JSON.stringify({ value: 2 }),
    );
  });
});
