import { queryOptions, useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

export function interfaceLabelsQO({ filters, select }) {
  const queryKey = ['common:interface-labels'];
  if (filters) queryKey.push(filters);

  return queryOptions({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey,
    queryFn: ({ signal }) =>
      api.gms.getInterfaceLabels({ signal, params: queryKey[1] }),
    staleTime: Infinity,
    select: select ?? selectIfLabelTransOptions,
  });
}

export const useInterfaceLabels = (args) => {
  return useQuery(args);
};

function selectIfLabelTransOptions({ data }) {
  const byId = {};
  const arr = [];

  Object.entries(data.wan).forEach(([labelId, o]) => {
    arr.push(labelId);

    byId[labelId] = {
      value: labelId,
      label: `${o.name} (wan)`,
      network: 'wan',
    };
  });
  Object.entries(data.lan).forEach(([labelId, o]) => {
    arr.push(labelId);
    byId[labelId] = {
      value: labelId,
      label: `${o.name} (lan)`,
      network: 'lan',
    };
  });

  return { od: data, byId, arr };
}
