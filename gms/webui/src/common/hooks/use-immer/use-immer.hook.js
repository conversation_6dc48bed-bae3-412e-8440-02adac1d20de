import { useCallback, useState } from 'react';
import { produce } from 'immer';

const useImmer = (initialState) => {
  const [state, setState] = useState(initialState);

  const updateState = useCallback((updater) => {
    if (typeof updater === 'function') {
      setState(produce(updater));
    } else {
      setState(updater);
    }
  }, []);

  return [state, updateState];
};

export { useImmer };
