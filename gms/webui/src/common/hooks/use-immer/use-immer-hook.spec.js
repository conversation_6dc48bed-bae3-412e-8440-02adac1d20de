import { describe, expect, it } from 'vitest';
import { renderHook, act } from '@test-utils';

import { useImmer } from './use-immer.hook.js';

describe('Common Hook: useImmer', async () => {
  it('returns default state and function to update the state', () => {
    const {
      result: {
        current: [state, setState],
      },
    } = renderHook(() => useImmer());

    expect(state).toBeUndefined();
    expect(typeof setState).toBe('function');
  });

  it('returns value with initial state and function to update the state', () => {
    const { result } = renderHook(() => useImmer({ value: 1 }));

    expect(result.current[0]).toStrictEqual({ value: 1 });
  });

  it('returns updated state value if setFunction is called with new value', () => {
    const { result } = renderHook(() => useImmer({ value: 1 }));

    act(() => {
      result.current[1]((draft) => {
        draft.value += 1;
      });
    });

    expect(result.current[0]).toStrictEqual({ value: 2 });
  });
});
