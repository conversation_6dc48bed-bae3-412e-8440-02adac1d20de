import { useLayoutEffect, useState } from 'react';
import { useDebounce } from '@common/hooks';

export const useElementHeight = ({
  parentElementRef,
  minHeight,
  maxHeight,
  otherElementsHeight,
}) => {
  // Keep the Table the height of the parent.
  const [elementHeight, setElementHeight] = useState(minHeight);
  const [resizeTable] = useDebounce(
    () => {
      const node = parentElementRef.current;
      if (!node) {
        return;
      }
      const { height } = node.getBoundingClientRect();

      let calcElementHeight = height - otherElementsHeight;

      // Set min & max height if valid min & max values are provided
      if (minHeight && minHeight > 0 && calcElementHeight < minHeight) {
        calcElementHeight = minHeight;
      } else if (
        maxHeight &&
        maxHeight > (minHeight || 0) &&
        calcElementHeight > maxHeight
      ) {
        calcElementHeight = maxHeight;
      }

      setElementHeight(calcElementHeight);
    },
    100,
    {
      trailing: true,
      maxWait: 100,
    },
  );

  useLayoutEffect(() => {
    resizeTable();
    window.addEventListener('resize', resizeTable);

    return () => {
      window.removeEventListener('resize', resizeTable);
    };
  }, [resizeTable]);

  return elementHeight;
};
