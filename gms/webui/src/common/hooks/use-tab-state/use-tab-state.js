import PropTypes from 'prop-types';

import { usePersistState } from '../use-persist-state/use-persist-state.hook.js';
import { getTabStateKey, validateHookParams } from '@common/utils';

const useTabState = (key, initialState) => {
  validateHookParams(useTabState, { key });
  return usePersistState(getTabStateKey(key), initialState);
};

useTabState.propTypes = {
  key: PropTypes.string.isRequired,
};

export { useTabState as useGridFiltersState };
