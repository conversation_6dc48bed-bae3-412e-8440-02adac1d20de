import { describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';

import { useGridFiltersState } from './use-tab-state.js';

const localStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorage,
});

describe('Common Hook: useGridFiltersState', async () => {
  it('returns default state and function to update the state', () => {
    const {
      result: {
        current: [state, setState],
      },
    } = renderHook(() => useGridFiltersState('test-key', { value: 1 }));

    expect(state).toStrictEqual({ value: 1 });
    expect(typeof setState).toBe('function');
    expect(localStorage.getItem).toBeCalledWith('WEBUI_test-key#tabState');
  });

  it('updates state value and saves in localstorage on setFunction call - object', () => {
    const { result } = renderHook(() =>
      useGridFiltersState('test-key', { value: 1 }),
    );

    act(() => {
      result.current[1]((draft) => {
        draft.value = 2;
      });
    });

    expect(result.current[0]).toStrictEqual({ value: 2 });
    expect(localStorage.setItem).toBeCalledWith(
      'WEBUI_test-key#tabState',
      JSON.stringify({ value: 2 }),
    );
  });
});
