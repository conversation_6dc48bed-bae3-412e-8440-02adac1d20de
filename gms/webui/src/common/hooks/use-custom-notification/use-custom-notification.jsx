import { Button, notification, Space } from 'antd';
import { useCallback } from 'react';

function useCustomNotification() {
  const [api, contextHolder] = notification.useNotification();

  const renderButtons = (buttons, key) => {
    return (
      <Space>
        {buttons.map((btn, index) => (
          <Button
            key={index}
            type={btn.type || 'default'}
            onClick={() => {
              if (btn.click?.fn) btn.click.fn();
              if (btn.click?.close !== false) api.destroy(key);
            }}>
            {btn.label}
          </Button>
        ))}
      </Space>
    );
  };

  const openNotification = useCallback(
    (type) => {
      return (message, description, buttons = [], config = {}) => {
        const isButtons = buttons.length > 0;

        const duration = isButtons
          ? 0
          : ['error', 'warning'].includes(type)
            ? 6
            : 4;

        const { key = `open${Date.now()}`, ...restConfig } = config;

        api[type]({
          style: { minWidth: 250, maxWidth: 350 }, // just like we are using for appMessage
          message,
          description,
          placement: 'topRight',
          closeIcon: false,
          duration,
          actions: isButtons ? renderButtons(buttons, key) : null,
          key,

          ...restConfig,
        });
      };
    },
    [api],
  );

  return {
    contextHolder,

    success: openNotification('success'),
    error: openNotification('error'),
    warning: openNotification('warning'),
    info: openNotification('info'),
  };
}

export { useCustomNotification };
