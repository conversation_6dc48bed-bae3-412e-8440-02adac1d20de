import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';
import { api } from '@sdk';

import { useLoopbackOrch } from './use-loopback-orch.hook';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useLoopbackOrch', async () => {
  it('calls get api', async () => {
    const axiosGet = vi.spyOn(api.loopbackOrch, 'get');

    await act(() => {
      renderHook(() => useLoopbackOrch());
    });

    expect(axiosGet).toHaveBeenCalled();
  });
});
