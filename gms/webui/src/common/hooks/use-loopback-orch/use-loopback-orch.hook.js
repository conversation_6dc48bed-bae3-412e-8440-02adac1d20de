import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

function useLoopbackOrch() {
  const {
    data: loopbackOrchData,
    isLoading,
    isError,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ['common:loopback-orch'],
    queryFn: api.loopbackOrch.get,
    select: ({ data }) => data,
  });

  return {
    loopbackOrchData,
    isLoading,
    isError,
    isSuccess,
    refetch,
  };
}

export { useLoopbackOrch };
