import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useDispatch } from 'react-redux';
import { api } from '@sdk';

import { setAuth } from '@features/login/auth.slice';

function useLoginStatus() {
  const dispatch = useDispatch();

  const { data: loginStatusData, isSuccess: isLoginStatusSuccess } = useQuery({
    queryKey: ['login-status'],
    queryFn: api.auth.getLoginStatus,
  });

  useEffect(() => {
    if (isLoginStatusSuccess) {
      dispatch(setAuth(loginStatusData.data));
    }
  }, [dispatch, isLoginStatusSuccess, loginStatusData]);

  return { loginStatusData, isLoginStatusSuccess };
}

export { useLoginStatus };
