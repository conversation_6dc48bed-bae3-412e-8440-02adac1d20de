import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

function useStatsCollector() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['common:stats-collector'],
    queryFn: ({ signal }) => api.statsCollector.getMigrationStatus({ signal }),
    select: (response) => response.data,
  });

  return {
    scMigrationStatus: data,
    isSCEnabled: data?.legacyStatsCollectionDisabled, //This is true only when legacy sc is disabled
    isLoading,
    error,
  };
}

export { useStatsCollector };
