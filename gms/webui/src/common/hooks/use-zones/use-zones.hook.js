import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

/**
 * Hook for getting current zones list
 *
 * @param {Object} gridKey
 * @return {Object} {data, isLoading, zones, excludeDefaultZones}
 */
function useZones(gridKey) {
  const { t } = useTranslation();
  const [zones, setZones] = useState([]);
  const [excludeDefaultZones, setExcludeDefaultZones] = useState([]);

  const { data, isLoading } = useQuery({
    queryKey: [gridKey],
    queryFn: api.zones.get,
    select: (response) => response.data,
  });

  useEffect(() => {
    if (isLoading) return;
    const zones = Object.values(data).map((zone, index) => ({
      label: zone.name,
      value: zone.name,
      id: ++index,
    }));
    zones.unshift({ label: t('default'), value: 'default', id: 0 });
    setZones(zones);
    setExcludeDefaultZones(zones.filter((item) => item.id !== 0));
  }, [isLoading, data, t]);

  return { data, isLoading, zones, excludeDefaultZones };
}

export { useZones };
