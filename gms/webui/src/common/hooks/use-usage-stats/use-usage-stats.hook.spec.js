import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';
import { api } from '@sdk';

import { useUsageStats } from './use-usage-stats.hook.js';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useUsageStats', () => {
  it('returns default props, default handlers, default state and default formatters', async () => {
    const axiosPost = vi.spyOn(api.uiUsageStats, 'post');

    await act(() => {
      return renderHook(() => useUsageStats('Logging_tab'));
    });

    expect(axiosPost).toHaveBeenCalledWith(null, {
      params: { uiName: 'Logging_tab' },
    });
  });
});
