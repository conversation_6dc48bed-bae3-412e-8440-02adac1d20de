import { useMutation } from '@tanstack/react-query';
import { useEffect } from 'react';
import { api } from '@sdk';

const useUsageStats = (uiName) => {
  const { mutate } = useMutation({
    mutationFn: ({ uiName }) =>
      api.uiUsageStats.post(null, { params: { uiName } }),
  });

  useEffect(() => {
    if (uiName) {
      mutate({ uiName });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export { useUsageStats };
