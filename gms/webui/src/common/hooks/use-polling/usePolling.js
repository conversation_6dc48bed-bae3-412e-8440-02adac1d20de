import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { api } from '@sdk';

function usePolling(apiFunction, requestConfigProvider) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const apiEtagHash = useRef(null);

  const isSuccess = useMemo(() => {
    // by default consider 400 so that isSuccess flag will be false.
    const apiStatus = data?.status ?? 400;
    return apiStatus >= 200 && apiStatus < 400;
  }, [data]);

  const pollingResponseCallback = useCallback((error, response) => {
    setLoading(false);
    apiEtagHash.current = response?.headers?.etag;

    if (error) {
      setError(error);
      setData(null);
      return;
    }

    // As 304 HTTP status indicates no change in response so
    // use the same old response data returned previously.
    if (response.status === 304) {
      return;
    }

    setError(null);
    setData(response);
  }, []);

  const refetch = useCallback(() => {
    apiFunction().then(
      (resp) => pollingResponseCallback(null, resp),
      (error) => pollingResponseCallback(error),
    );
  }, [apiFunction, pollingResponseCallback]);

  const requestConfigProviderFn = useCallback(() => {
    setLoading(true);

    const customRequestConfig = requestConfigProvider?.() ?? {};

    if (apiEtagHash.current) {
      customRequestConfig.headers = {
        ...customRequestConfig.headers,

        // This header is necessary to avoid the un-necessary data fetching
        // if data is not changed across the API calls.
        'If-None-Match': apiEtagHash.current,
      };
    }

    return customRequestConfig;
  }, [requestConfigProvider]);

  const onPollingStart = useCallback(() => {
    apiEtagHash.current = null;
  }, []);

  useEffect(() => {
    // Register request config provider
    api.registerRequestConfigProvider(apiFunction, requestConfigProviderFn);
    api.on(apiFunction, 'startPolling', onPollingStart);

    return () => {
      api.removeRequestConfigProvider(apiFunction);
    };
  }, [apiFunction, requestConfigProviderFn, onPollingStart]);

  useEffect(() => {
    // Subscribe to API polling
    const uniqueSubscriberId = api.subscribe(
      apiFunction,
      pollingResponseCallback,
    );

    // Unsubscribe on unmount
    return () => {
      if (uniqueSubscriberId) {
        api.unsubscribe(apiFunction, uniqueSubscriberId);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { data, loading, isSuccess, error, refetch };
}

export { usePolling };
