import { get } from 'radash';
import PropTypes from 'prop-types';

import { useApplianceError } from '../use-appliance-error/use-appliance-error.hook';
import { useAppliances } from '../use-appliances/use-appliances.hook';
import { getHostName } from '@common/utils';

const useNoData = (errors, message) => {
  const { showError } = useApplianceError();
  const { selectedAppliances } = useAppliances();

  if (!errors) {
    return {};
  }

  const getError = () => {
    const noVersionData = [];
    const filteredErrors = errors?.filter(
      (error) => get(error?.response?.status, '') !== 404,
    );
    if (filteredErrors?.length) {
      showError(errors);
    }

    errors?.forEach((error) => {
      if (get(error?.response?.status, '') === 404) {
        const errorData = {
          applianceName: getHostName(
            selectedAppliances,
            error.config.params?.nePk,
          ),
          nePk: error.config.params?.nePk || '',
          noData: message,
        };
        noVersionData.push(errorData);
        return errorData;
      }
    });

    return noVersionData.length > 0 ? noVersionData : [];
  };

  return { getError };
};

useNoData.propTypes = {
  errors: PropTypes.array,
};

export { useNoData };
