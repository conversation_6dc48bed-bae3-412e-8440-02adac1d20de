import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook, act } from '@test-utils';
import { api } from '@sdk';

import { useGmsRegistration } from './use-gms-registration.hook';

beforeEach(() => {
  vi.restoreAllMocks();
});

describe('Common Hook: useGmsRegistration', () => {
  it('returns gms registration information', async () => {
    const axiosGet = vi.spyOn(api.gms.registration, 'get');

    await act(() => {
      renderHook(() => useGmsRegistration({ gmsRegistrationEnabled: true }));
    });

    expect(axiosGet).toHaveBeenCalled();
  });
});
