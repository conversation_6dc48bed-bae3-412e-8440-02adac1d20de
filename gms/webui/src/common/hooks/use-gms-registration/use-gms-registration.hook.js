import { useQuery } from '@tanstack/react-query';
import { api } from '@sdk';

function useGmsRegistration({ gmsRegistrationEnabled }) {
  const {
    data: gmsRegistrationData,
    isLoading: isGmsRegistrationLoading,
    isError: isGmsRegistrationError,
    error: gmsRegistrationError,
    refetch: gmsRegistrationRefetch,
  } = useQuery({
    queryKey: ['common:gms-registration'],
    queryFn: api.gms.registration.get,
    select: ({ data }) => data,
    enabled: gmsRegistrationEnabled,
  });

  return {
    gmsRegistrationData,
    isGmsRegistrationLoading,
    isGmsRegistrationError,
    gmsRegistrationError,
    gmsRegistrationRefetch,
  };
}

export { useGmsRegistration };
