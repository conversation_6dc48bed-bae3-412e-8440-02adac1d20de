const sortBoost = (a, b, asc) => {
  const NA = 'N/A';
  const invalidA = !a || a === NA || a === 'Not Granted';
  const invalidB = !b || b === NA || b === 'Not Granted';
  if (invalidA && invalidB) {
    return 1;
  } else if (invalidA) {
    return 1;
  } else if (invalidB) {
    return -1;
  } else {
    let boostA = parseInt(a.split(' ')[0], 10);
    let unitsA = a.split(' ')[1];
    if (unitsA === 'Gbps') {
      boostA *= 1000000;
    }
    if (unitsA === 'Mbps') {
      boostA *= 1000;
    }

    let boostB = parseInt(b.split(' ')[0], 10);
    let unitsB = b.split(' ')[1];
    if (unitsB === 'Gbps') {
      boostB *= 1000000;
    }
    if (unitsB === 'Mbps') {
      boostB *= 1000;
    }

    return asc * (boostA === boostB ? 0 : boostA > boostB ? 1 : -1);
  }
};

export { sortBoost };
