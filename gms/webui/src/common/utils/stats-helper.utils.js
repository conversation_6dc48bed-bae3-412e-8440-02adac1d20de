import dayjs from 'dayjs';

const isValidTimestampRange = (minTimestamp, maxTimestamp) => {
  return minTimestamp && maxTimestamp && minTimestamp !== maxTimestamp;
};

const isValidDateRange = (aStart, aEnd, qStart, qEnd) => {
  return (
    aStart >= qStart &&
    aEnd <= qEnd &&
    aStart instanceof Date &&
    aEnd instanceof Date
  );
};

const getGapAllowance = (granularity) => {
  const allowances = {
    day: 2 * 60 * 60 * 24,
    hour: 2 * 60 * 60,
    default: 2 * 60,
  };
  return allowances[granularity] || allowances.default;
};

const formatTimestamp = (timestamp) => {
  return dayjs.unix(timestamp).format('DD MMM YY h:mm A');
};

export const getActualDateRange = (
  queryStartTime,
  queryEndTime,
  availableMinTimestamp,
  availableMaxTimestamp,
  granularity,
) => {
  if (!isValidTimestampRange(availableMinTimestamp, availableMaxTimestamp)) {
    return;
  }

  const qStart = new Date(queryStartTime * 1000);
  const qEnd = new Date(queryEndTime * 1000);
  const aStart = new Date(availableMinTimestamp * 1000);
  const aEnd = new Date(availableMaxTimestamp * 1000);

  if (!isValidDateRange(aStart, aEnd, qStart, qEnd)) {
    return;
  }

  const gapAllowance = getGapAllowance(granularity);
  const startTimeGap = Math.abs(queryStartTime - availableMinTimestamp);
  const endTimeGap = Math.abs(queryEndTime - availableMaxTimestamp);

  if (startTimeGap > gapAllowance || endTimeGap > gapAllowance) {
    return {
      startTime: formatTimestamp(availableMinTimestamp),
      endTime: formatTimestamp(availableMaxTimestamp),
    };
  }
};

export const getTimeRange = (queryObject) => {
  const multiplier = getMultiplier(queryObject);
  let startTime = queryObject.startTime - (queryObject.startTime % multiplier);

  const length = (queryObject.endTime - startTime) / multiplier;
  return Array.from({ length }, (_, i) => startTime + i * multiplier);
};

export const getMultiplier = (queryObject) => {
  const granularity = getGranularity(queryObject);
  const multipliers = {
    day: 24 * 60 * 60,
    hour: 60 * 60,
    default: 60,
  };
  return multipliers[granularity] || multipliers.default;
};

export const getCurrentTimeRange = (params, cb) => {
  const updatedParams = { ...params };

  if (updatedParams.timeSegment === 'Custom' || updatedParams.realTime) {
    cb(null, updatedParams);
    return;
  }

  const granularity = updatedParams.isTrendsCharts
    ? getGranularity(updatedParams)
    : updatedParams.granularity || 'hour';

  const currentDate = new Date();
  currentDate.setSeconds(0);
  if (
    (granularity === 'hour' ||
      granularity === 'day' ||
      updatedParams.truncateMinutes) &&
    updatedParams.isTrendsCharts
  ) {
    currentDate.setMinutes(0);
  }

  const currentTime = Math.floor(currentDate.getTime() / 1000);
  const timeSegments = {
    '1hr': 3600,
    '4hr': 4 * 3600,
    '8hr': 8 * 3600,
    '1d': 24 * 3600,
    '2d': 2 * 24 * 3600,
    '3d': 3 * 24 * 3600,
    '7d': 7 * 24 * 3600,
    '30d': 30 * 24 * 3600,
  };

  updatedParams.startTime =
    currentTime - timeSegments[updatedParams.timeSegment];
  updatedParams.endTime = currentTime;

  if (
    (granularity === 'hour' || granularity === 'day') &&
    !updatedParams.isTrendsCharts
  ) {
    const extraTime = granularity === 'hour' ? 3600 : 24 * 3600;
    updatedParams.startTime -= currentTime % extraTime !== 0 ? extraTime : 0;
  }

  updatedParams.str = Object.entries(updatedParams)
    .map(([key, val]) => `${key}=${val}`)
    .join('&');

  cb(null, updatedParams);
};

/**
 * we hack granularity for time series charts because for 1hr or 4hrs we have only 1 or 4 data points respectively in hourly table.
 * so instead of getting data from hourly table for 1hr & 4hrs we get data from minute table and for 1day and 7days we get data from hourly table.
 */
export const getGranularity = (query) => {
  if (query.granularity === 'minute') {
    return 'minute';
  }
  if (query.timeSegment !== 'Custom') {
    return query.timeSegment?.endsWith('hr') ? 'minute' : 'hour';
  }
  return query.overrideToHourlyGranularity ? 'hour' : query.granularity;
};

export const getBytesRateMultiplier = (queryObject) => {
  const granularity = getGranularity(queryObject);
  const multipliers = {
    day: 8 / (24 * 60 * 60),
    hour: 8 / (60 * 60),
    default: 8 / 60,
  };
  return multipliers[granularity] || multipliers.default;
};

export const getReductionPercentage = (firstValue, secondValue) => {
  if (firstValue === 0) return 0;
  const tempValue = Math.floor(((firstValue - secondValue) * 100) / firstValue);
  return Math.max(tempValue, 0);
};

export const convertTimeIntoScheduleTimezone = (
  timestamp,
  inReportMode,
  scheduleTimezone,
) => {
  if (inReportMode) {
    const date = new Date(timestamp);
    return new Date(
      date.toLocaleString('en-US', {
        timeZone: scheduleTimezone.defaultTimezone,
      }),
    );
  } else {
    return new Date(timestamp);
  }
};
