import { languages } from '@common/constants';

const lang = languages.EN;

const collator = new Intl.Collator(lang, {
  numeric: true,
  sensitivity: 'base',
});

const includesAll = (sourceArray, destinationArray) =>
  destinationArray.every((val) => sourceArray.includes(val));

Array.prototype.includesAll = function (destinationArray) {
  return (
    this.length === destinationArray.length &&
    this.every((val) => destinationArray.includes(val))
  );
};

Array.prototype.last = function () {
  return this[this.length - 1];
};

export { collator, includesAll };
