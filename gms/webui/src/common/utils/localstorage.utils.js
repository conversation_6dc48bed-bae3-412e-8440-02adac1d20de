import { isPrimitive } from 'radash';
import { modulePrefix } from '../constants';

export const getModKey = (key) => modulePrefix + key;

export const getModuleObj = (key) => {
  const item = localStorage.getItem(key);
  return JSON.parse(item || '{}');
};

export const setLocalStorageItem = (key, value, objKey) => {
  const modKey = getModKey(key);
  if (objKey) {
    const obj = getModuleObj(modKey);
    obj[objKey] = value;
    localStorage.setItem(modKey, JSON.stringify(obj));
  } else if (!isPrimitive(value)) {
    localStorage.setItem(modKey, JSON.stringify(value));
  } else {
    localStorage.setItem(modKey, value);
  }
};

export const getLocalStorageItem = (key, defaultValue, objKey) => {
  const modKey = getModKey(key);
  let value;
  if (objKey) {
    const obj = getModuleObj(modKey);
    value = obj[objKey];
  } else {
    value = localStorage.getItem(modKey);
  }
  if (!value && defaultValue) {
    setLocalStorageItem(key, defaultValue, objKey);
    return defaultValue;
  }

  return value;
};

/**
 * This function removes single key from the local storage.
 *
 * @param {string} key Key to be removed from local storage
 * @param {string} objKey Key inside the object stored in local-storage
 */
export const deleteLocalStorageItem = (key, objKey) => {
  const modKey = getModKey(key);
  if (objKey) {
    const obj = getModuleObj(key);
    delete obj[objKey];
    localStorage.setItem(modKey, JSON.stringify(obj));
  } else {
    localStorage.removeItem(modKey);
  }
};

/**
 * This function removes multiple keys from the local storage.
 *
 * @param {string[]} keys Keys to be removed from local storage
 * @param {string} objKey Key inside the object stored in local-storage
 */
export const deleteLocalStorageItems = (keys, objKey) => {
  keys.forEach((key) => {
    deleteLocalStorageItem(key, objKey);
  });
};

export const getUsableLocalStorageValue = (localStorageItem) => {
  if (!localStorageItem) return {};
  return typeof localStorageItem === 'object'
    ? localStorageItem
    : JSON.parse(localStorageItem);
};
