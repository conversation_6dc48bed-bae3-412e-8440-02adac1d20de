/**
 * Formats a number with an appropriate suffix (e.g., K, M, G, T) based on its value.
 * @param {number} val - The number to format.
 * @param {string} [suffix='bps'] - The suffix to append to the formatted number.
 * @returns {string} - Formatted number with suffix.
 */

export const numberSuffixFormatter = (val, suffix = '', allowDecimal) => {
  const suffixes = ['', 'K', 'M', 'G', 'T'];
  let label = +val;

  let power = 0;
  while (label >= 1000 && power < suffixes.length - 1) {
    label /= 1000;
    power++;
  }

  label = label >= 10 && !allowDecimal ? label.toFixed() : label.toFixed(1);

  if (val < 1000) {
    return `${val}${suffix}`;
  }

  return `${label}${suffixes[power]}${suffix}`;
};
