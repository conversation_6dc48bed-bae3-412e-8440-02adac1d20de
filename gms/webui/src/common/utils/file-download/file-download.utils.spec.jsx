import { describe, expect, it } from 'vitest';

import { toBase64 } from './file-download.utils.js';

describe('Common Utils: File Download', async () => {
  describe('toBase64', () => {
    it('should return base64 formatted string when passed some file content', async () => {
      const file = new File(['sample'], 'sample.png', { type: 'image/png' });
      const result = await toBase64(file);
      expect(result).toContain('data:image/png;base64');
    });
  });
});
