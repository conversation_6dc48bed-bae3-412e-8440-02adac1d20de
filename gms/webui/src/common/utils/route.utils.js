import { regex } from '../constants';

/**
 * function to get route prefix based on version
 * @returns {string}
 */
const getRoutePrefix = () => {
  const versionString = location.pathname.split('/')?.[1];

  const routePrefix = regex.APP_VERSION.test(versionString)
    ? `/${versionString}`
    : '';

  return routePrefix;
};

export const getHostUrl = () => {
  const hostUrl = `${window.location.protocol}//${window.location.hostname}`;
  return hostUrl;
};

const switchToOldApp = (name, type = 'tab') => {
  const routePrefix = getRoutePrefix();

  window.open(
    `${routePrefix}/webclient/html/gms_main.html?${type}=${name}`,
    'ArubaOrchestratorLegacy',
  );
};
export { getRoutePrefix, switchToOldApp };
