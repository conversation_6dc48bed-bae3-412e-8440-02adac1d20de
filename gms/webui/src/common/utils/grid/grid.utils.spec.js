import { describe, expect, it } from 'vitest';

import { getColumnDefs } from './grid.utils.js';

describe('Common Utils: Grid', () => {
  describe('getColumnDefs', () => {
    it('should add headerTooltips and valueTooltips by default', () => {
      const columns = [
        {
          field: 'id',
          headerName: 'Alarm ID',
          width: 100,
          minWidth: 100,
        },
        {
          field: 'hostName',
          headerName: 'Host Name',
          width: 100,
          minWidth: 100,
          filter: 'agSetColumnFilter',
        },
      ];

      const result = getColumnDefs(columns);

      expect(result[0].headerTooltip).toBe('Alarm ID');
      expect(result[0].tooltipField).toBe('id');

      expect(result[1].headerTooltip).toBe('Host Name');
      expect(result[1].tooltipField).toBe('hostName');
    });

    it('should skip tooltip if already added', () => {
      const columns = [
        {
          field: 'id',
          headerName: 'Alarm ID',
          width: 100,
          minWidth: 100,
          headerTooltip: 'Alarm',
        },
        {
          field: 'hostName',
          headerName: 'Host Name',
          width: 100,
          minWidth: 100,
          tooltipField: 'id',
        },
      ];

      const result = getColumnDefs(columns);

      expect(result[0].headerTooltip).toBe('Alarm');
      expect(result[0].tooltipField).toBe('id');

      expect(result[1].headerTooltip).toBe('Host Name');
      expect(result[1].tooltipField).toBe('id');
    });
  });
});
