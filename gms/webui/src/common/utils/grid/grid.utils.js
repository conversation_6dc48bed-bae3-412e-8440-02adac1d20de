import { getGridStateKey } from '@common/utils/grid-hook.utils';
import { getLocalStorageItem } from '@common/utils/localstorage.utils';

import { dateComparator } from '../date.utils';
import { isEmpty } from 'radash';

/**
 * function to add common column definitions
 * @param {*} columns
 * @param {*} configuration
 * @returns updated columns
 */
export function getColumnDefs(columns, configuration = {}) {
  const { headerTooltips = true, valueTooltips = true } = configuration;

  return columns.map((column) => {
    const updatedColumn = { ...column };

    if (headerTooltips && !column.headerTooltip) {
      updatedColumn.headerTooltip = column.headerName;
    }

    if (
      valueTooltips &&
      !column.tooltipField &&
      !(column.valueGetter || column.valueFormatter)
    ) {
      updatedColumn.tooltipField = column.field;
    }

    if (valueTooltips && (column.valueGetter || column.valueFormatter)) {
      updatedColumn.tooltipValueGetter =
        column.valueFormatter || column.valueGetter;
    }

    if (column.filter) {
      updatedColumn.filterParams = {
        buttons: ['reset', 'apply'],
        closeOnApply: true,
        ...column.filterParams,
      };
      if (column.filter === 'agSetColumnFilter') {
        updatedColumn.filterParams = {
          ...updatedColumn.filterParams,
        };
      } else if (column.filter === 'agDateColumnFilter') {
        updatedColumn.filterParams = {
          comparator: (date1, date2) => dateComparator(date2, date1),
          ...updatedColumn.filterParams,
        };
      }
    }

    if (column.headerClass) {
      updatedColumn.toolPanelClass = column.headerClass;
    }

    return updatedColumn;
  });
}

const GRID_COLUMN_FILTERS = Object.freeze({
  string: 'agTextColumnFilter',
  number: 'agNumberColumnFilter',
  set: 'agSetColumnFilter',
  date: 'agDateColumnFilter',
});

export const getColumnDef = (obj, width, filter, isFromShaper = false) => {
  const header = isFromShaper ? obj.label : [obj.label, obj.suffix].join(' ');
  const def = {
    headerName: header,
    field: obj.name,
    flex: 1,
    minWidth: width,
    width,
  };

  if (filter) {
    def.filter = GRID_COLUMN_FILTERS[filter];

    if (filter === 'number') {
      def.cellClass = 'textRight';
    }
  }

  return def;
};

/**
 * Function to change the column visibility when grid rendered.
 * Hide columns and refresh/close tab, open again.
 * See columns selection, it reflects user's last preference.
 * @param {*} event - Event of onGridSizeChanged
 * @param {*} gridKey - To get the local storage key for last preference
 */
export function handleGridColSelectionState(event, gridKey) {
  const localKey = getGridStateKey(gridKey);
  const localState = JSON.parse(getLocalStorageItem(localKey)) ?? {};
  if (!isEmpty(localState?.columnVisibility?.hiddenColIds)) {
    event.api.setColumnsVisible(
      localState?.columnVisibility?.hiddenColIds,
      false,
    );
  }
}

/**
 * Function to change the column sort when grid rendered initially.
 * 2 columns are getting sort arrows when last preference was not default.
 * @param {*} initialState - Last preference data from localstorage
 * @param {*} columnDefs - Column defs for the grid
 * @returns updated column definitions
 */
export function handleMultiSortIcons(initialState, columnDefs) {
  const currentSort = initialState?.sort?.sortModel?.[0];
  return columnDefs.map((column) => {
    if (currentSort) {
      if (column.sort && currentSort?.colId !== column.field) {
        delete column.sort;
      }
      if (currentSort?.colId === column.field) {
        column.sort = currentSort.sort;
      }
    }
    return column;
  });
}

/**
 * Function to adjust the column width to occupy available space.
 * @param {*} event - Event of onColumnResized
 */
export function handleGridEmptySpace(event) {
  if (
    event.finished &&
    ['autosizeColumns', 'toolPanelUi'].includes(event.source)
  ) {
    const allVisibleCols = event.api
      .getAllDisplayedColumns()
      .filter((c) => c.colDef.field !== 'actions');
    const lastCol = allVisibleCols[allVisibleCols.length - 1];
    if (lastCol) {
      event.api.applyColumnState({
        state: [{ colId: lastCol.colId, flex: 1 }],
        applyOrder: false,
      });
    }
  }
}
