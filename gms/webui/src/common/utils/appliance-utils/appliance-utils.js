import { t } from 'i18next';

import {
  APPLIANCE_STATE_ENUM,
  APPLIANCE_REACHABILITY,
} from '@common/constants';

/**
 * Return hostName
 * @param {Array} selectedAppliances
 * @param {string} nePk
 * @returns {string}
 */
export const getHostName = (selectedAppliances, nePk) => {
  const appliance = selectedAppliances.find(
    (appliance) => appliance.nePk === nePk,
  );
  return appliance ? appliance.hostName : null;
};

/**
 * Generates an array of options for appliances dropdown options.
 * @param {Array<Object>} appliances - An array of appliance objects.
 * @returns {Array<Object>} An array of option objects for appliances.
 */
export const applianceOptions = (appliances) =>
  appliances.map((item) => ({ label: item.hostName, value: item.id }));

/**
 * Return appliance based on status
 * @param {Array} selectedAppliances
 * @param {state} state
 * @returns {Array}
 */
export const getAppliancesByState = (selectedAppliances, state) => {
  const appliances = selectedAppliances.filter(
    (appliance) => appliance.state === state,
  );
  return appliances;
};

export function getAppliancePlatforms() {
  return [
    t(`sidebar.platforms.amazonEc2`),
    t(`sidebar.platforms.googleCloud`),
    t(`sidebar.platforms.msAzure`),
    t(`sidebar.platforms.oracleCloud`),
    t(`sidebar.platforms.vmware`),
    t(`silverPeak`), // This key is present in common-ns
  ];
}

export function getApplianceStateTranslation(state) {
  const translations = {
    [APPLIANCE_STATE_ENUM.UNKNOWN]: t('unknown'),
    [APPLIANCE_STATE_ENUM.NORMAL]: t('normal'),
    [APPLIANCE_STATE_ENUM.UNREACHABLE]: t('unReachable'),
    [APPLIANCE_STATE_ENUM.UNSUPPORTED_VERSION]: t(`sidebar.unsupportedVersion`),
    [APPLIANCE_STATE_ENUM.OUT_OF_SYNC]: t(`sidebar.notSynchronized`),
    [APPLIANCE_STATE_ENUM.SYNC_IN_PROGRESS]: t(`sidebar.syncInProgress`),
    [APPLIANCE_STATE_ENUM.CONNECTED]: t('connected'),
    [APPLIANCE_STATE_ENUM.NOT_CONNECTED]: t('notConnected'),
  };

  return translations[state] ?? translations[APPLIANCE_STATE_ENUM.UNKNOWN];
}

export function getApplianceReachabilityTranslation(reachability) {
  switch (reachability) {
    case APPLIANCE_REACHABILITY.REACHABLE: {
      return t(`reachable`);
    }
    case APPLIANCE_REACHABILITY.UNREACHABLE: {
      return t(`unReachable`);
    }
    case APPLIANCE_REACHABILITY.PARTIALLY_REACHABLE: {
      return t(`partiallyReachable`);
    }
    default:
      return '';
  }
}

/**
 * It constructs and opens an hyperlink from gms to vxoa
 *
 * This function will create an URL using nePk of the appliance and if there are any query params then those will be appended to the URL.
 *
 * @param {Object} param0
 * @param {string} param0.nePk The appliance nePk
 * @param {function} param0.popupBlockerNotifier If popup is blocked then this callback will be called
 * @param {object} [param0.queryParameters={}] Additional query parameters to send in URL
 */
export function openHyperlink({
  nePk,
  popupBlockerNotifier,
  queryParameters = {},
}) {
  const gmsHost = window.location.hostname;

  const hasQueryParams = !!Object.keys(queryParameters).length;
  const searchParamsObj = new URLSearchParams(queryParameters);
  const baseUrl = `https://${gmsHost}/gms/rest/vxoaChannel/${nePk}/loader.php`;
  const url = hasQueryParams
    ? `${baseUrl}?${searchParamsObj.toString()}`
    : baseUrl;

  const win = window.open(url, nePk, 'noopener=1');
  if (!win) {
    popupBlockerNotifier();
  } else {
    win.focus();
    setTimeout(function () {
      if (!document.hidden) {
        // if still on the same window, close and re-open to focus
        win.close();
        window.open(url, nePk).focus();
      }
    }, 500);
  }
}

export function openCliSession(nePk, popupBlockerNotifier) {
  const gmsIp = window.location.hostname;
  const url =
    'https://' + gmsIp + '/webclient/html/cliSession.html?nePk=' + nePk;
  const win = window.open(url, null, 'noopener=1');
  if (!win) {
    popupBlockerNotifier();
  } else {
    win.focus();
    setTimeout(function () {
      if (!document.hidden) {
        win.close();
        window.open(url).focus();
      }
    }, 500);
  }
}
