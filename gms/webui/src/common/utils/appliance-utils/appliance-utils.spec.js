import { describe, expect, it } from 'vitest';
import { getHostName } from './appliance-utils';

describe('Common:getHostName', () => {
  it('Should return hostname based on nePk value', () => {
    const results = getHostName(
      [{ nePk: '0.NE', hostName: 'jyothika-ecva' }],
      '0.NE',
    );
    expect(results).toBe('jyothika-ecva');
  });
  it('Should return null when passed nePk value not found', () => {
    const results = getHostName(
      [{ id: '0.NE', hostName: 'jyothika-ecva' }],
      '0.NE',
    );
    expect(results).toBe(null);
  });
});
