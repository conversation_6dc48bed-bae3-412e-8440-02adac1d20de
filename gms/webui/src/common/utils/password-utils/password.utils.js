import { t } from 'i18next';
import { isEmpty } from 'radash';

export const EMPTY_PASSWORD_MASK = '****';
export const isValidMasked = (maskedValue) => {
  return isEmpty(maskedValue) || maskedValue === EMPTY_PASSWORD_MASK;
};

export const getInvalidPasswordMessage = (
  passType,
  password,
  minPasswordLength,
) => {
  if (!isValidMasked(password) && !/[A-Z]/.test(password)) {
    return t('passwordMustContainUppercaseCharacter', { passType });
  } else if (!isValidMasked(password) && !/[a-z]/.test(password)) {
    return t('passwordMustContainLowercaseCharacter', { passType });
  } else if (!isValidMasked(password) && !/[0-9]/.test(password)) {
    return t('passwordMustContainDigit', { passType });
  } else if (!isValidMasked(password) && !/[^a-zA-Z\d]/.test(password)) {
    return t('passwordMustContainSpecialCharacter', { passType });
  } else if (
    !isValidMasked(password) &&
    !(password.length >= minPasswordLength)
  ) {
    return t('passwordMustContainMinLength', {
      passType,
      minLength: minPasswordLength,
    });
  }
};
