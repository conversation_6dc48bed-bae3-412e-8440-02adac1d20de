import { describe, expect, it } from 'vitest';

import {
  checkIPv4v6Format,
  checkIpFormat,
  inetAtoN,
  isValidInetHostname,
} from './ip.utils.js';

describe('Common Utils: Formatters', () => {
  describe('inetAtoN', () => {
    it('should return 0 when passed 0.0.0.0', () => {
      const result = inetAtoN('0.0.0.0');
      expect(result).toBe(0);
    });
  });
  describe('checkIpFormat', () => {
    it('should return 0 when passed 0.0.0.0', () => {
      const result = checkIpFormat('0.0.0.0');
      expect(result).toBe(true);
    });
    it('should return 0 when passed *******', () => {
      const result = checkIpFormat('*******');
      expect(result).toBe(true);
    });
    it('should return 0 when passed 8.8.4', () => {
      const result = checkIpFormat('8.8.4');
      expect(result).toBe(false);
    });
  });
  describe('Checking the validations utility function isValidInetHostname()', () => {
    it('should return null when passed valid hostname', () => {
      const result = isValidInetHostname('time.nist.gov');
      expect(result).toBe(null);
    });
    it('should return error message when passed empty string', () => {
      const result = isValidInetHostname('');
      expect(result).toBe("Hostname can't be empty");
    });
    it('should return invalid warning message when passed hostname starting with "-"', () => {
      const result = isValidInetHostname('-time.nist.go');
      console.log(result);
      expect(result).toBe("Hostname can't start with '-'");
    });
    it('should return warning message when passed hostname with invalid characters', () => {
      const result = isValidInetHostname('Meross\x1aair\x2aPurifier.local');
      expect(result).toBe('Hostname contains invalid characters');
    });
  });
  describe('Checking the validations utility function checkIPv4v6Format()', () => {
    it('should return true when passed valid ipv4 & ipv6 address', () => {
      const result = checkIPv4v6Format(
        '2001:db8:3333:4444:5555:6666:7777:8888',
      );
      expect(result).toBe(true);
    });
    it('should return false when passed invalid ipv4 & ipv6 address', () => {
      const result = checkIPv4v6Format('fe80:2030:31:24');
      expect(result).toBe(false);
    });
  });
});
