export const getBandwidthFromKBitsDecimal = (value) => {
  if (typeof value === 'string') {
    value = parseInt(value, 10);
  }
  if (value >= 1000 * 1000 * 1000) {
    value = (value / (1000 * 1000 * 1000)).toFixed(1) + ' Tbps';
  } else if (value >= 1000 * 1000) {
    value = (value / (1000 * 1000)).toFixed(1) + ' Gbps';
  } else if (value >= 1000) {
    value = (value / 1000).toFixed(1) + ' Mbps';
  } else {
    value = value.toFixed(1) + ' Kbps';
  }

  return value;
};

export const convertKBitsBandwidthUnit = (value, unit) => {
  if (typeof value === 'string') {
    value = parseInt(value, 10);
  }
  if (unit === undefined) {
    return value + ' Kbps';
  }

  if (unit === 'Tbps') {
    return (value / (1000 * 1000 * 1000)).toFixed(1) + ' ' + unit;
  }
  if (unit === 'Gbps') {
    return (value / (1000 * 1000)).toFixed(1) + ' ' + unit;
  }
  if (unit === 'Mbps') {
    return (value / 1000).toFixed(1) + ' ' + unit;
  }
  return value + ' Kbps';
};

export const calcAvailBoost = (used, total) => {
  if (typeof used === 'string') {
    used = parseInt(used, 10);
  }
  if (typeof total === 'string') {
    total = parseInt(total, 10);
  }

  // format the unit for available boost with the same unit as used or total boost
  let suffix = '';
  if (used > 0) {
    const usedFormatted = getBandwidthFromKBitsDecimal(used);
    suffix = usedFormatted.replace(/\d\.*/g, '').trim();
  } else {
    const totalFormatted = getBandwidthFromKBitsDecimal(total);
    suffix = totalFormatted.replace(/\d\.*/g, '').trim();
  }
  return convertKBitsBandwidthUnit(total - used, suffix);
};
