import { commonStrings } from '@common/constants';

export const isReleaseNewer = (release1, release2) => {
  if (release1[0] === 0) return true;

  for (let i = 0; i < release1.length; i++) {
    if (release1[i] > release2[i]) return true;
    if (release1[i] < release2[i]) return false;
  }
  // Release1 less than Release2
  return false;
};

// pad the array with 0 up to 5 numbers
// if we have more than 5 numbers in array, it'll be truncated
// NOTICE: this function will change the param passed in
export const padRelease = (release) => {
  let len = release.length;
  release.length = 5;
  release.fill(0, len);
};

export const isReleaseNewerOrSame = (release1, release2) => {
  // trunk is always allowed
  if (release1[0] === 0) return true;

  for (let i = 0; i < release1.length; i++) {
    if (release1[i] > release2[i]) return true;
    if (release1[i] < release2[i]) return false;
  }
  // Releases are identical
  return true;
};

export const relStrToNums = (release) => {
  if (!release) return null;
  let buildNumArr = release.trim().split('.');
  let patchSvn = (buildNumArr && buildNumArr[3]?.split('_')) || [];

  buildNumArr?.splice(3, 2, ...patchSvn);

  return buildNumArr?.map((numstr) => parseInt(numstr, 10));
};

/**
 * Checks if the supplied version meets the minimum version requirements passed in
 * ex: ver = 6.2.5.0_00000
 * applianceMinVersion(ver, 6, 2, 6) returns false
 * applianceMinVersion(ver, 6, 2, 5) returns true
 * @param version
 * @param ...release
 */
export const supportsVersion = (version, ...release) => {
  if (!version) return false;

  padRelease(release);
  return isReleaseNewerOrSame(relStrToNums(version), release);
};

export const getApplianceSVersion = (nepk, allAppliances) => {
  let retVal = commonStrings.UNKNOWN;
  allAppliances?.forEach((item) => {
    if (item.id === nepk) {
      retVal = item.softwareVersion;
      return false;
    }
  });
  return retVal;
};

export const supportsBuildRelease = (release, requiredRelease) => {
  if (release && requiredRelease) {
    return isReleaseNewerOrSame(
      relStrToNums(release),
      relStrToNums(requiredRelease),
    );
  } else return false;
};

export const getApplianceState = (nepk, allAppliances) => {
  let retVal = '-1';
  allAppliances?.find((item) => {
    if (item.id === nepk) {
      retVal = item.state;
      return false;
    }
  });
  return retVal;
};

export const supportsVersionLimitStream = (version, limit, ...release) => {
  if (!version) return false;

  if (limit > 5) limit = 5;
  padRelease(release);

  let verNumArr = relStrToNums(version);
  padRelease(verNumArr);

  for (let i = 0; i < limit; i++) {
    if (verNumArr[i] != release[i]) return false;
  }

  return isReleaseNewerOrSame(verNumArr, release);
};
