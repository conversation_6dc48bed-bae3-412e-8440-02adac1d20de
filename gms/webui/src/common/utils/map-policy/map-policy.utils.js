import { t } from 'i18next';
import { min, max, diff, list } from 'radash';
import { getReqRangeRule } from '@common/components/form-item/form-item.utils';
import { CONFIGURATION_NS } from '@common/constants';

const NS = CONFIGURATION_NS.MAP_POLICY;
const T = (key, ...args) => t(`${NS}:${key}`, ...args);

const DEFAULT_PRIO = 10;
export const DEFAULT_MAX_PRIO = 65535;
const ORCH_TEMPL_LOW_LIMIT = 1000;
const ORCH_TEMPL_HIGH_LIMIT = 9999;
const MIN_PRIORITY_OVERLAY_MANAGER_RULE = 1000 * 20;
const MAX_PRIORITY_OVERLAY_MANAGER_RULE = 1000 * 25 - 1;

export const getNextPriority = (
  isTemplate,
  priorities,
  excludePriorities = [],
) => {
  if (isTemplate) {
    return priorities.length > 0
      ? max(priorities) + DEFAULT_PRIO
      : ORCH_TEMPL_LOW_LIMIT;
  } else {
    // filtering out all the priorities which are in orchestrator reserved range, max priority and
    const filteredPriorities = priorities.filter(
      (priority) =>
        !isInOrchestratorReservedRange(priority) &&
        priority !== DEFAULT_MAX_PRIO &&
        !excludePriorities.includes(priority),
    );

    let nextPriority = DEFAULT_PRIO;
    if (filteredPriorities.length > 0) {
      const filteredMaxPriority = max(filteredPriorities);

      let nextPossiblePriority =
        filteredMaxPriority - (filteredMaxPriority % 10) + DEFAULT_PRIO;

      if (isInTemplateRange(nextPossiblePriority)) {
        nextPriority = ORCH_TEMPL_HIGH_LIMIT + 1;
      } else if (isInOverlayRange(nextPossiblePriority)) {
        nextPriority = MAX_PRIORITY_OVERLAY_MANAGER_RULE + 1;
      } else if (nextPossiblePriority >= DEFAULT_MAX_PRIO) {
        nextPriority = min(diff(list(1, DEFAULT_MAX_PRIO), filteredPriorities));
      } else {
        nextPriority = nextPossiblePriority;
      }

      if (excludePriorities.includes(nextPriority)) {
        nextPriority =
          max(excludePriorities) - (max(excludePriorities) % 10) + 10;
      }
    }

    return nextPriority;
  }
};

export const isInTemplateRange = (priority) =>
  priority >= ORCH_TEMPL_LOW_LIMIT && priority <= ORCH_TEMPL_HIGH_LIMIT;

export const isInOverlayRange = (priority) =>
  priority >= MIN_PRIORITY_OVERLAY_MANAGER_RULE &&
  priority <= MAX_PRIORITY_OVERLAY_MANAGER_RULE;

export const isInOrchestratorReservedRange = (priority) =>
  isInTemplateRange(priority) || isInOverlayRange(priority);

const getTemplateRangeValidationObj = (value) => {
  const isError = isInTemplateRange(value);

  const errorMessage = isError
    ? T('rangeErrMsg', {
        min: ORCH_TEMPL_LOW_LIMIT,
        max: ORCH_TEMPL_HIGH_LIMIT,
      })
    : '';

  return { isError, errorMessage };
};

const getOverlayRangeValidationObj = (value) => {
  const isError = isInOverlayRange(value);

  const errorMessage = isError
    ? T('rangeErrMsg', {
        min: MIN_PRIORITY_OVERLAY_MANAGER_RULE,
        max: MAX_PRIORITY_OVERLAY_MANAGER_RULE,
      })
    : '';

  return { isError, errorMessage };
};

const getMustBeUniqueValidationObj = (value, existingPriorities, record) => {
  const isError =
    existingPriorities.includes(value) && value != record.priority;

  const errorMessage = isError ? T('priorityMustBeUniq') : '';

  return { isError, errorMessage };
};

const templateValidationFns = [getMustBeUniqueValidationObj];
const tabValidationFns = [
  ...templateValidationFns,
  getTemplateRangeValidationObj,
  getOverlayRangeValidationObj,
];

const validatorFn = (
  existingPriorities,
  record,
  fns = templateValidationFns,
) => {
  return (_, value) => {
    for (const fn of fns) {
      const { isError, errorMessage } = fn(value, existingPriorities, record);

      if (isError) {
        return Promise.reject(errorMessage);
      }
    }

    return Promise.resolve();
  };
};

export const getPriorityRules = (isTemplate, existingPriorities, record) => {
  if (isTemplate) {
    return [
      ...getReqRangeRule(ORCH_TEMPL_LOW_LIMIT, ORCH_TEMPL_HIGH_LIMIT),

      {
        validator: validatorFn(
          existingPriorities,
          record,
          templateValidationFns,
        ),
      },
    ];
  } else {
    return [
      ...getReqRangeRule(1, DEFAULT_MAX_PRIO),
      {
        validator: validatorFn(existingPriorities, record, tabValidationFns),
      },
    ];
  }
};
