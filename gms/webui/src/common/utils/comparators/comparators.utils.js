/**
 * to compare appliances version
 * @param {string} a
 * @param {string} b
 * @returns {number}
 */
export const compareVxoaVersion = (a, b) => {
  let x = a.trim();
  let y = b.trim();
  if (x.match(/^\d+\.\d+\.\d+\.\d+_\d+$/g) === null) {
    return -1;
  }
  if (y.match(/^\d+\.\d+\.\d+\.\d+_\d+$/g) === null) {
    return 1;
  }
  let xParts = x.trim().split('.');
  let xPatchSvn = xParts[3].split('_');
  let yParts = y.trim().split('.');
  let yPatchSvn = yParts[3].split('_');
  let xComponents = [
    parseInt(xParts[0], 10),
    parseInt(xParts[1], 10),
    parseInt(xParts[2], 10),
    parseInt(xPatchSvn[0], 10),
    parseInt(xPatchSvn[1], 10),
  ];
  let yComponents = [
    parseInt(yParts[0], 10),
    parseInt(yParts[1], 10),
    parseInt(yParts[2], 10),
    parseInt(yPatchSvn[0], 10),
    parseInt(yPatchSvn[1], 10),
  ];

  if (xComponents[0] === 0 && yComponents[0] === 0) {
    return xComponents[4] - yComponents[4];
  } else if (xComponents[0] === 0) {
    return 1;
  } else if (yComponents[0] === 0) {
    return -1;
  }

  for (let i = 0; i < xComponents.length; i++) {
    if (xComponents[i] > yComponents[i]) {
      return 1;
    }
    if (xComponents[i] < yComponents[i]) {
      return -1;
    }
  }
  return 0;
};

/**
 * to compare float values
 * @param {string} a
 * @param {string} b
 * @returns {number}
 */
export const compareFloat = (a, b) => {
  const value1 = parseFloat(a);
  const value2 = parseFloat(b);
  if (value1 === value2) return 0;
  return value1 > value2 ? 1 : -1;
};

export const compareStrings = (valueA, valueB) => {
  const normalizedA = valueA?.trim()?.toLowerCase();
  const normalizedB = valueB?.trim()?.toLowerCase();

  if (normalizedA < normalizedB) return -1;
  if (normalizedA > normalizedB) return 1;
  return 0;
};

export const mixedValueComparator = (valueA, valueB) => {
  const numA = parseFloat(valueA);
  const numB = parseFloat(valueB);

  const isNumA = !isNaN(numA);
  const isNumB = !isNaN(numB);

  if (isNumA && isNumB) {
    return numA - numB;
  }
  if (isNumA) return -1;
  if (isNumB) return 1;
  return valueA.localeCompare(valueB);
};
