import { describe, expect, it } from 'vitest';
import { compareVxoaVersion } from './comparators.utils';

describe('Common Utils: Comparators', () => {
  describe('compareVxoaVersion', () => {
    it('should return Yes when passed same values', () => {
      const result = compareVxoaVersion('8.1.9.21_78366', '8.1.9.21_78366');
      expect(result).toBe(0);
    });
  });

  describe('compareVxoaVersion', () => {
    it('should return -1 when passed different values', () => {
      const result = compareVxoaVersion('8.1.9.21_78366', '8.1.9.21_78367');
      expect(result).toBe(-1);
    });
  });
});
