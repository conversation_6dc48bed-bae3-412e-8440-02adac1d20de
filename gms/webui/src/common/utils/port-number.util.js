function isPortNumberInRange(portNumber) {
  const parsedPortNumber = parseInt(portNumber);
  return !(parsedPortNumber < 0 || parsedPortNumber > 65535);
}

/**
 * This function validates the port number and returns error message on validation failure
 * or return nothing if validation succeeds.
 *
 * @param {string} portNumber Port number input as a string
 * @returns Error message or nothing
 */
export function validatePortNumber(portNumber) {
  const PORT_RANGE_REG = /^([0-9]+)-([0-9]+)$/;
  const PORT_NUMBER_REG = /^\d+$/;
  const rangeExec = PORT_RANGE_REG.exec(portNumber);
  const numberExec = PORT_NUMBER_REG.test(portNumber);

  if (portNumber.indexOf('-') >= 0) {
    //check range rule eg: [111-117]
    if (!rangeExec) {
      return 'Invalid range values';
    } else if (parseInt(rangeExec[1]) >= parseInt(rangeExec[2])) {
      return 'Range values must be in increasing order';
    } else if (
      !isPortNumberInRange(rangeExec[1]) ||
      !isPortNumberInRange(rangeExec[2])
    ) {
      return 'port number must be between 0 and 65535';
    }
  } else if (!numberExec || !isPortNumberInRange(portNumber)) {
    //check number rule
    return 'port number must be between 0 and 65535';
  }

  return null;
}
