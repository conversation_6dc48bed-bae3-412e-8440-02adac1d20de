import { describe, expect, it } from 'vitest';

import { checkDomainFormat } from './domain.utils.js';

describe('Common Utils: domain', () => {
  describe('checkDomainFormat', () => {
    it('should return false when passed -google.com', () => {
      const result = checkDomainFormat('-google.com');
      expect(result).toBe(false);
    });
    it('should return true when passed google.com', () => {
      const result = checkDomainFormat('google.com');
      expect(result).toBe(true);
    });
  });
});
