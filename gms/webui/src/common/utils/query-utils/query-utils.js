import dayjs from 'dayjs';
import { isArray, isObject } from 'radash';

/**
 * Default Utility function to combine query results
 *
 * @param {Array} results
 * @returns {Object} {results, data, pending, refetch, isError}
 */
export const queryCombineDefaultFn = (results) => {
  const data = [];
  const refetch = [];
  const loading = [];
  const errors = [];
  let isError = false;
  let isLoading = false;
  results.forEach((result = {}) => {
    if (result.data && result.isError === false) {
      if (result.data && isArray(result.data)) {
        data.push(...result.data);
      } else if (result.data && isObject(result.data)) {
        data.push({ ...result.data });
      }
    }
    refetch.push(result.refetch);

    if (result.isError) {
      isError = true;
      errors.push(result?.error);
    }

    if (result.isLoading || result.isFetching) {
      isLoading = true;
      loading.push(result.isLoading);
    }
  });

  return {
    results,
    data,
    refetch,
    isError,
    isLoading,
    errors,
    isLoadingCount: loading.length,
    lastRefreshTime: dayjs(),
  };
};
