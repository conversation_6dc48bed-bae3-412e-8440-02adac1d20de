import { describe, expect, it } from 'vitest';
import { queryCombineDefaultFn } from './query-utils';

describe('Common: Query utils', () => {
  it('Should return object when we pass array of objects', () => {
    const result = queryCombineDefaultFn([
      {
        status: 'success',
        fetchStatus: 'idle',
        isPending: false,
        isSuccess: true,
        isError: false,
        isInitialLoading: false,
        isLoading: false,
        data: [
          {
            1: {
              name: '',
              value: '',
            },
            2: {
              name: '',
              value: '',
            },
          },
        ],
        dataUpdatedAt: 1720431432953,
        error: null,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: true,
        isFetchedAfterMount: true,
        isFetching: false,
        isRefetching: false,
        isLoadingError: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetchError: false,
        isStale: true,
      },
      {
        status: 'error',
        fetchStatus: 'idle',
        isPending: false,
        isSuccess: false,
        isError: true,
        isInitialLoading: false,
        isLoading: false,
        dataUpdatedAt: 0,
        error: {
          message: 'Request failed with status code 404',
          name: 'AxiosError',
          stack:
            'AxiosError: Request failed with status code 404\n    at settle (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1203:12)\n    at XMLHttpRequest.onloadend (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1420:7)\n    at Axios.request (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1780:41)',
          config: {
            transitional: {
              silentJSONParsing: true,
              forcedJSONParsing: true,
              clarifyTimeoutError: false,
            },
            adapter: ['xhr', 'http'],
            transformRequest: [null],
            transformResponse: [null],
            timeout: 0,
            xsrfCookieName: 'XSRF-TOKEN',
            xsrfHeaderName: 'X-XSRF-TOKEN',
            maxContentLength: -1,
            maxBodyLength: -1,
            env: {},
            headers: {
              Accept: 'application/json, text/plain, */*',
            },
            withCredentials: true,
            baseURL: 'https://localhost/gms/rest/',
            params: {
              nePk: '11.NE',
              cached: true,
              _: 1720431432892,
            },
            method: 'get',
            url: '/snmp',
          },
          code: 'ERR_BAD_REQUEST',
          status: 404,
        },
        errorUpdatedAt: 1720431433239,
        failureCount: 1,
        failureReason: {
          message: 'Request failed with status code 404',
          name: 'AxiosError',
          stack:
            'AxiosError: Request failed with status code 404\n    at settle (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1203:12)\n    at XMLHttpRequest.onloadend (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1420:7)\n    at Axios.request (https://localhost:3000/node_modules/.vite/deps/axios.js?v=b2c60450:1780:41)',
          config: {
            transitional: {
              silentJSONParsing: true,
              forcedJSONParsing: true,
              clarifyTimeoutError: false,
            },
            adapter: ['xhr', 'http'],
            transformRequest: [null],
            transformResponse: [null],
            timeout: 0,
            xsrfCookieName: 'XSRF-TOKEN',
            xsrfHeaderName: 'X-XSRF-TOKEN',
            maxContentLength: -1,
            maxBodyLength: -1,
            env: {},
            headers: {
              Accept: 'application/json, text/plain, */*',
            },
            withCredentials: true,
            baseURL: 'https://localhost/gms/rest/',
            params: {
              nePk: '11.NE',
              cached: true,
              _: 1720431432892,
            },
            method: 'get',
            url: '/snmp',
          },
          code: 'ERR_BAD_REQUEST',
          status: 404,
        },
        errorUpdateCount: 1,
        isFetched: true,
        isFetchedAfterMount: true,
        isFetching: true,
        isRefetching: false,
        isLoadingError: true,
        isPaused: false,
        isPlaceholderData: false,
        isRefetchError: false,
        isStale: true,
      },
    ]);
    expect(result).toBeTypeOf('object');
  });
});
