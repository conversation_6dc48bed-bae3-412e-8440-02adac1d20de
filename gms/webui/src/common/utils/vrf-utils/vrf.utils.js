import { MIN_VRF_SUPPORT } from '@common/constants/vrf.constants';
import { getApplianceSVersion, supportsBuildRelease } from '../release.utils';

const DEFAULT_SEGMENT_NAME = 'Default';
const invalidTxt = 'Deleted';
const DEFAULT_SEGMENT_ID = 0;

/**
 * Retrieves the zone name based on the given zone ID.
 *
 * @param {Array} vrfSegmentZonesMap - List of zone objects containing zoneId and zoneName.
 * @param {number|string} zoneId - The ID of the zone to find.
 * @returns {string|number} - The name of the zone if found; otherwise, returns the zoneId.
 */
export const getZoneNameByZoneId = (vrfSegmentZonesMap = [], zoneId) => {
  const zoneObj = vrfSegmentZonesMap.find((item) => item.zoneId === zoneId);
  return zoneObj ? zoneObj.zoneName : zoneId;
};

/**
 * Retrieves the zone ID from the given VRF segment name and zone name.
 *
 * @param {Array} vrfSegmentZonesMap - List of zone objects containing vrfName, zoneName, and zoneId.
 * @param {string} segmentName - The name of the VRF segment.
 * @param {string} zoneName - The name of the zone.
 * @returns {number|undefined} - The ID of the zone if found; otherwise, undefined.
 */
export const getZoneIdFromSegmentZoneName = (
  vrfSegmentZonesMap = [],
  segmentName,
  zoneName,
) => {
  const zoneObj = vrfSegmentZonesMap.find(
    (item) => item.vrfName === segmentName && item.zoneName === zoneName,
  );
  return zoneObj?.zoneId;
};

/**
 * Retrieves the VRF segment name from its ID.
 *
 * @param {number} id - The ID of the VRF segment.
 * @param {Array|Object} segmentList - List or object containing segment details.
 * @returns {string} - The name of the VRF segment if found; otherwise, an empty string.
 */
export const vrfIdToName = function (id, segmentList) {
  if (id === 0) return DEFAULT_SEGMENT_NAME;
  let ret = '';
  if (Array.isArray(segmentList)) {
    let found = segmentList.find((e) => e.id == id);
    if (found) ret = found.name;
  } else {
    if (segmentList) {
      let segmentObj = segmentList[id];
      if (segmentObj && segmentObj.name) {
        ret = segmentObj.name;
      }
    }
  }
  return ret;
};

/**
 * Retrieves the VRF segment ID from its name.
 *
 * @param {string} name -  Name of VRF segment.
 * @param {Array|Object} segmentList - List or object containing segment details.
 * @returns {number} - The ID of the VRF segment if found; otherwise, -1.
 */
export const vrfNameToId = function (name, segmentList) {
  if (name === DEFAULT_SEGMENT_NAME) return 0;
  let ret = -1;
  if (Array.isArray(segmentList)) {
    let found = segmentList.find((e) => e.label === name);
    if (found) ret = found.id;
  } else {
    if (segmentList) {
      for (const id in segmentList) {
        if (segmentList[id].name === name) {
          ret = id; // Since it's an object, we return the key as the id
          break;
        }
      }
    }
  }
  return Number(ret);
};

/**
 * Retrieves the VRF segment name associated with a given zone ID.
 *
 * @param {Array} vrfSegmentZonesMap - List of zone objects containing zoneId and vrfName.
 * @param {number|string} zoneId - The ID of the zone.
 * @returns {string|number} - The name of the VRF segment if found; otherwise, returns the zoneId.
 */
export const getSegmentNameByZoneId = (vrfSegmentZonesMap = [], zoneId) => {
  const zoneObj = vrfSegmentZonesMap.find((item) => item.zoneId === zoneId);
  return zoneObj ? zoneObj.vrfName : zoneId;
};

/**
 * Checks if VRF is supported for the selected appliance.
 *
 * @param {string} nePk - The network equipment primary key.
 * @param {Array} allAppliances - List of all appliances.
 * @returns {boolean} - True if VRF is supported; otherwise, false.
 */
export const supportsVRF = (nePk, allAppliances) => {
  return supportsBuildRelease(
    getApplianceSVersion(nePk, allAppliances),
    MIN_VRF_SUPPORT,
  );
};

/**
 * Retrieves the name used for an invalid segment.
 *
 * @returns {string} - The default name representing a deleted or invalid segment.
 */
export const getInvalidSegmentName = function () {
  return invalidTxt;
};

export const defaultSegmentId = function () {
  return DEFAULT_SEGMENT_ID;
};

export const defaultSegmentName = function () {
  return DEFAULT_SEGMENT_NAME;
};
