import { describe, expect, it, vi } from 'vitest';

import {
  noDataCellClass,
  noDataColSpan,
  noDataFormatter,
  numberFormatter,
  passwordFormatter,
  waitingFormatter,
  yesNoFormatter,
} from './formatters.utils.js';

describe('Common Utils: Formatters', () => {
  describe('yesNoFormatter', () => {
    it('should return Yes when passed value is true', () => {
      const result = yesNoFormatter({ value: true });
      expect(result).toBe('Yes');
    });

    it('should return No when passed value is false', () => {
      const result = yesNoFormatter({ value: false });
      expect(result).toBe('No');
    });
  });
  describe('noDataFormatter', () => {
    it('Should return noData when we passed ', () => {
      const result = noDataFormatter({
        value: '************',
        data: {
          noData:
            'This appliance version does not support custom appliance tag',
        },
      });
      expect(result).toBe(
        'This appliance version does not support custom appliance tag',
      );
    });
    it('Should return No data available text when noData is not a string ', () => {
      const result = noDataFormatter({
        value: '************',
        data: {
          noData: 9,
        },
      });
      expect(result).toBe('No data available');
    });
  });
  describe('noDataColSpan', () => {
    it('Should return number of columns to be merged', () => {
      const result = noDataColSpan({
        api: {
          getColumnDefs: () => [{}, {}],
        },
        data: {
          appl_name: 'host_9_NE',
          noData: 'No PoE support',
        },
      });
      expect(result).toBeTypeOf('number');
    });
  });

  describe('waitingFormatter', () => {
    it('Should return loading string', () => {
      const result = waitingFormatter(vi.fn(), null, {
        value: '',
        colDef: {
          headerName: 'Secondary Segment',
        },
        data: {
          applianceName: 'jyothikae-ecva',
          nePk: '0.NE',
        },
      });
      expect(result).toBeTypeOf('string');
    });
    it('Should return original formatter', () => {
      const result = waitingFormatter(
        () => true,
        () => 'result',
        {
          value: '',
          colDef: {
            headerName: 'Secondary Segment',
          },
          data: {
            id: 5,
            applianceName: 'jyothikae-ecva',
            nePk: '0.NE',
            getProcessed: false,
          },
        },
      );
      expect(result).toBeTypeOf('string');
    });
  });

  describe('noDataCellClass', () => {
    it('Should return CSS Style if we have noData Value otherwise empty array', () => {
      const result = noDataCellClass({
        data: {
          noData:
            'This appliance version does not support custom appliance tag',
        },
      });
      expect(result).toBeTypeOf('object');
    });
  });
  describe('numberFormatter', () => {
    it('Should return number with comma saperator when we pass value', () => {
      const result = numberFormatter(8496549854385);
      expect(result).toBeTypeOf('string');
      expect(result).toBe('8,496,549,854,385');
    });
  });
  describe('passwordFormatter', () => {
    it('Should return formatted value when we pass length of the value', () => {
      const result = passwordFormatter(5);
      expect(result).toBe('•••••');
    });
  });
});
