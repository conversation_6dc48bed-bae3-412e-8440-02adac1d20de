import { t } from 'i18next';
import { isString } from 'radash';
import { commonStrings, regex } from '@common/constants';
import { dateTimeComparator } from '../date.utils';

const { NO_DATA_AVAILABLE, LOADING } = commonStrings;

export const NOT_APPLICABLE = 'N/A';

/**
 * Formatter function to get 'Yes' or 'No' based on boolean value
 *
 * @memberOf Utils
 * @param {object} params
 * @returns
 */
export const yesNoFormatter = ({ value }) => {
  return value ? t('yes') : t('no');
};

export const yesNoNAFormatter = ({ value }) => {
  return value === undefined ? NOT_APPLICABLE : yesNoFormatter(value);
};

/**
 * Return String based on noData value
 *
 * @memberOf Utils
 * @param {string} value
 * @param {object} data
 * @returns {string}
 */
export const noDataFormatter = ({ value, data: dataContext }, noDataKey) => {
  if (dataContext?.[noDataKey || 'noData']) {
    value = isString(dataContext[noDataKey || 'noData'])
      ? dataContext[noDataKey || 'noData']
      : NO_DATA_AVAILABLE;
  }
  return value || '';
};

/**
 * Return Object based on noData value
 *
 * @memberOf Utils
 * @param {object} data
 * @returns {object}
 */
export const sortNoDataToLast = (data) => {
  const withNoData = [];
  const withoutNoData = [];

  data.forEach((item) => {
    if (item.noData) {
      withNoData.push(item);
    } else {
      withoutNoData.push(item);
    }
  });

  const finalData = [...withoutNoData, ...withNoData];
  return finalData;
};

/**
 * Return number based on sort value
 *
 * @memberOf Utils
 * @param {string} firstString
 * @param {string} secondString
 * @param {object} nodeA
 * @param {object} nodeB
 * @param {boolean} sortDirection
 * @returns {number}
 */
export const customComparator = (
  firstString,
  secondString,
  nodeA,
  nodeB,
  sortDirection,
  noDataKey,
  isDate = false,
) => {
  if (!sortDirection) {
    if (firstString === undefined && secondString === undefined) return 0;
    if (firstString === undefined) return 1;
    if (secondString === undefined) return -1;
  }

  if (sortDirection) {
    if (firstString === '' && secondString === '') return 0;
    if (firstString === '' && secondString === undefined) return 1;
    if (firstString === undefined && secondString === '') return -1;
    if (firstString === '') return -1;
    if (secondString === '') return 1;
  }
  if (
    nodeA?.data?.[noDataKey || 'noData'] &&
    !nodeB?.data?.[noDataKey || 'noData']
  ) {
    return 0;
  }
  if (
    !nodeA?.data?.[noDataKey || 'noData'] &&
    nodeB?.data?.[noDataKey || 'noData']
  ) {
    return sortDirection ? 1 : -1;
  }

  if (isDate) {
    return dateTimeComparator(firstString, secondString);
  }

  if (firstString < secondString) return -1;
  if (firstString > secondString) return 1;
  return 0;
};

/**
 * Returns number of columns to be merged
 *
 * @memberOf Utils
 * @param {object} api
 * @param {object} data
 * @returns {number}
 */
export const noDataColSpan = ({ api, data }, noDataKey) => {
  if (data[noDataKey || 'noData']) {
    return api.getColumnDefs().length;
  }
  return 1;
};

/**
 * Return CSS Style if we have  noData Value otherwise empty array
 *
 * @memberOf Utils
 * @param {object} data
 * @returns {Array}
 */
export const noDataCellClass = ({ data }, noDataKey) =>
  data[noDataKey || 'noData']
    ? ['textCenter', 'fontItalic', 'colorTextTertiary']
    : [];

/**
 * Return Loading... text when data is not present.
 *
 * @memberOf Utils
 * @param {Function} loadedTest
 * @param {Function} originalFormatter
 * @param {object} rowData
 * @param {string} value
 * @param {object} colDef
 */
export const waitingFormatter = (
  loadedTest,
  originalFormatter,
  { row, cell, value, colDef, data: rowData },
) => {
  if (!loadedTest(value, rowData)) {
    return LOADING;
  } else {
    if (originalFormatter)
      return originalFormatter(row, cell, value, colDef, rowData);
    else return value;
  }
};

/**
 * Return number with comma separator
 *
 * @memberOf Utils
 * @param {number} value
 * @returns {string}
 */
export const numberFormatter = (value) => {
  const parts = value.toString().split('.');
  parts[0] = parts[0].replace(regex.NUMBER_COMMA_SEPARATOR, ',');
  return parts.join('.');
};

/** Return formattedValue when pass format and length of the value
 *
 * @memberOf Utils
 * @param {number} length
 * @param {string} format
 */
export const passwordFormatter = (length, format = '•') => {
  return format.repeat(length);
};

export const valueFormatter = ({ value, data }) => {
  const unit = data?.units || '';

  return unit === '%' ? `${value}${unit}` : `${value} ${unit}`;
};

export const roundToDecimal = (value, decimals) => {
  if (isNaN(value)) {
    return 'NA';
  }
  const factor = Math.pow(10, decimals);
  return numberFormatter(Math.round(value * factor) / factor);
};

const units = [
  { threshold: 1000000000000000, suffix: 'P' },
  { threshold: 1000000000000, suffix: 'T' },
  { threshold: 1000000000, suffix: 'G' },
  { threshold: 1000000, suffix: 'M' },
  { threshold: 1000, suffix: 'K' },
];

export const getValueInUnit = (value, suffixEnd = '', precision = 0) => {
  if (value < 1000) {
    return !precision ? value : value.toFixed(precision);
  }

  for (const { threshold, suffix } of units) {
    if (value >= threshold) {
      value /= threshold;
      return value >= 10
        ? `${value.toFixed(0)}${suffix}${suffixEnd ?? ''}`
        : `${value.toFixed(1)}${suffix}${suffixEnd ?? ''}`;
    }
  }

  return value.toFixed(precision);
};

export const formatBytes = (value) => {
  if (value != undefined && !isNaN(1.0 * value) && value !== 0) {
    let t = `${value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} bytes`;
    if (value >= 1000) {
      t += ' ( ' + getValueInUnit(value, 'B') + ' )';
    }
    return t;
  }

  return '0 bytes';
};

export const formatPercentage = (value) => {
  if (value && !isNaN(1.0 * value) && value !== 0) {
    return value + ' %';
  }

  return '0 %';
};

export const numberWithCommas = (value) => {
  let parts = value.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
};

export const getUnitValueFromDecimal = (val) => {
  for (const unit of units) {
    if (val >= unit.threshold) {
      return (val / unit.threshold).toFixed(1) + unit.suffix;
    }
  }
  return val.toString();
};

export const suffixFormatter = (val, suffix = 'bps') => {
  let label = +val;
  for (const unit of units) {
    if (val >= unit.threshold) {
      label = val / unit.threshold;
      suffix = unit.suffix + suffix;
      break;
    }
  }
  return (label >= 10 ? label.toFixed() : label.toFixed(1)) + suffix;
};

export const storageFormatter = (value) => {
  let result;
  if (value >= Math.pow(2, 30)) {
    result = value / Math.pow(2, 30);
    if (Math.floor(result) < 10) {
      result = result.toFixed(1) + ' GB';
    } else {
      result = Math.floor(result) + ' GB';
    }
  } else if (value >= Math.pow(2, 20)) {
    result = value / Math.pow(2, 20);
    if (Math.floor(result) < 10) {
      result = result.toFixed(1) + ' MB';
    } else {
      result = Math.floor(result) + ' MB';
    }
  } else if (value >= Math.pow(2, 10)) {
    result = value / Math.pow(2, 10);
    if (Math.floor(result) < 10) {
      result = result.toFixed(1) + ' KB';
    } else {
      result = Math.floor(result) + ' KB';
    }
  } else {
    result = value;
    if (Math.floor(result) < 10) {
      result = result.toFixed(1) + ' KB';
    } else {
      result = Math.floor(result) + ' KB';
    }
  }
  return result;
};
