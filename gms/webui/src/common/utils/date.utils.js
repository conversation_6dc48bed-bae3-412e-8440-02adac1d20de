import { date } from '@common/constants';
import dayjs from '@config/dayjs/dayjs';
import { isNumber } from 'radash';

/**
 * Formats a given date and time using a specified format.
 * @param {number}
 * @returns {string}
 */

export const dateTimeFormatter = (
  dateTime,
  format = date.DISPLAY_DATE_TIME_FORMAT,
) => {
  return dateTime ? dayjs(dateTime).format(format) : undefined;
};

/**
 * Compares two date strings.
 * @method
 * @param {string} dateString1
 * @param {string} dateString2
 * @returns {number} - 0 if the dates are equal, 1 if the first date is greater, -1 if the second date is greater.
 */

const dateTimeComparatorHelper = (
  date1 = dayjs.unix(0),
  date2 = dayjs.unix(0),
  includeTime = true,
) => {
  const dayjsDate1 = includeTime ? dayjs(date1) : dayjs(date1).startOf('day');
  const dayjsDate2 = includeTime ? dayjs(date2) : dayjs(date2).startOf('day');

  if (dayjsDate1.isBefore(dayjsDate2)) {
    return -1;
  } else if (dayjsDate1.isAfter(dayjsDate2)) {
    return 1;
  } else {
    return 0;
  }
};

export const dateTimeComparator = (date1, date2) => {
  return dateTimeComparatorHelper(date1, date2);
};

export const dateComparator = (date1, date2) => {
  return dateTimeComparatorHelper(date1, date2, false);
};

/**
 * Formats a Unix timestamp to a date and time string.
 * @method
 * @param {number} timestamp - The Unix timestamp to format.
 * @param {string} [dateFormat=date.DISPLAY_DATE_TIME_FORMAT] - The format to use for the date and time.
 * @returns {string} - Formatted date and time string.
 */

export const dateTimeUnixFormatter = (
  timestamp,
  dateFormat = date.DISPLAY_DATE_TIME_FORMAT,
) => {
  return timestamp ? dayjs.unix(timestamp).format(dateFormat) : undefined;
};

/**
 * Formats a long duration value to days, hours, minutes, or seconds.
 * @method
 * @param {number} durationValue
 * @returns {string}
 */

export const formatLongToDays = (durationValue) => {
  const durationObject = dayjs.duration(durationValue);
  const days = Math.floor(durationObject.asDays());
  const hours = durationObject.hours();
  const minutes = durationObject.minutes();
  const seconds = durationObject.seconds();
  let result = '';
  if (days > 0) {
    result += `${days}d `;
  }
  if (hours > 0) {
    result += `${hours}h `;
  }
  if (minutes > 0) {
    result += `${minutes}m `;
  }
  if (seconds > 0) {
    result += `${seconds}s`;
  }
  return result;
};

export const timeFormatter = (params) => {
  const value = params.value;

  if (!value) {
    return '';
  }
  const formattedTime = dayjs
    .duration(value, 'seconds')
    .format(date.DURATION_TIME_FORMAT);

  return formattedTime;
};

const getDateFormat = (component, isSameDay) => {
  const baseFormat = { hour: '2-digit', minute: '2-digit', hour12: false };
  if (component === 'tooltip') {
    return {
      ...baseFormat,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      second: '2-digit',
    };
  }
  if (!isSameDay && component === 'tick') {
    return { ...baseFormat, day: 'numeric', month: 'short' };
  }
  return baseFormat;
};

export const getUTCTime = (timestamp, params, component) => {
  const isSameDay =
    new Date(params.endTime * 1000).toDateString() ===
    new Date(params.startTime * 1000).toDateString();
  const dateFormat = getDateFormat(component, isSameDay);
  dateFormat.timeZone = 'UTC';
  if (params.timeRange === 'Realtime') {
    dateFormat.second = '2-digit';
  }
  return new Date(parseInt(timestamp)).toLocaleTimeString('en-US', dateFormat);
};

export const getLocalTime = (timestamp, params, component) => {
  const isSameDay =
    new Date(params.endTime * 1000).toDateString() ===
    new Date(params.startTime * 1000).toDateString();
  const dateFormat = getDateFormat(component, isSameDay);
  if (params.timeRange === 'Realtime') {
    dateFormat.second = '2-digit';
  }
  return new Date(parseInt(timestamp)).toLocaleTimeString('en-US', dateFormat);
};

export const getNoDateTime = (params) => {
  const timeRanges = {
    '4hr': 60,
    '1d': 360,
    '7d': 24 * 60,
    Custom: 24 * 60,
    Realtime: 1,
  };
  const range = timeRanges[params.timeRange] || 10;
  const startTime = new Date(params.startTime).getTime();

  return Array.from({ length: 7 }, (_, i) => [
    (startTime + i * range * 60) * 1000,
    0,
  ]);
};

export const getUpdatedDateRangeFilterValues = (
  dateRange,
  stats,
  maxTime,
  minTime,
) => {
  dateRange.maxTimestamp = dateRange.maxTimestamp
    ? Math.max(maxTime, dateRange.maxTimestamp)
    : maxTime;
  dateRange.minTimestamp = dateRange.minTimestamp
    ? Math.min(minTime, dateRange.minTimestamp)
    : minTime;
  return dateRange;
};

export const millisecondsToHumanReadableFormatter = (value) => {
  if (isNaN(value)) {
    return 'NA';
  }
  if (value < 60000) {
    return '< 1m';
  }

  const units = [
    { label: 'd', value: 86400 }, // days
    { label: 'h', value: 3600 }, // hours
    { label: 'm', value: 60 }, // minutes
  ];

  let result = units
    .map((unit) => {
      const count = Math.floor(value / (unit.value * 1000));
      value %= unit.value * 1000;
      return count ? count + unit.label : '';
    })
    .filter(Boolean)
    .join(' ');

  return result || value; // return formatted result or original value if no valid time
};

export const TimeFormatter = (row, cell, valueInMills) => {
  if (valueInMills === 'N/A') {
    return valueInMills;
  } else if (valueInMills !== 0 && isNumber(valueInMills)) {
    const time = dayjs(valueInMills).tz(dayjs.tz.guess());
    const dayIsToday = dayjs(time).isSame(dayjs(), 'day');
    if (dayIsToday) {
      return time.format('HH:mm z');
    } else {
      return time.format('DD-MMM-YY HH:mm z');
    }
  } else {
    return '';
  }
};
/**
 * Returns a timestamp of given datem'Tue May 13 05:29:00 IST 2025' format
 * @param {string}
 * @returns {number}
 */
export const getParsedTime = (date) => {
  return dayjs(
    date
      .split(' ')
      .filter((_, i) => i !== 4)
      .join(' '),
  ).valueOf();
};
