import { supportsVersion, supportsVersionLimitStream } from '@common/utils';

export const supportsTierLicenses = (applianceVersion) => {
  // tier supported in 9.4+
  return (
    supportsVersion(applianceVersion, 9, 4) ||
    supportsVersionLimitStream(applianceVersion, 3, 8, 1, 9, 6) ||
    supportsVersionLimitStream(applianceVersion, 3, 8, 1, 7, 19)
  );
};

export const supportsFeatureLicenses = (applianceVersion) => {
  return supportsVersion(applianceVersion, 9, 4, 0, 0);
};

export const FeatureLicense = (
  featureKey,
  featureName,
  licenseType,
  unit,
  min,
  max,
  subFeatures,
) => {
  return {
    featureKey: featureKey,
    featureName: featureName,
    licenseType: licenseType,
    unit: unit,
    min: min ?? 0,
    max: max ?? (licenseType === 'quantity' ? Number.MAX_SAFE_INTEGER : 1),
    subFeatures: subFeatures
      ? subFeatures.map((feature) =>
          FeatureLicense(
            feature.featureKey,
            feature.featureName,
            feature.licenseType,
            feature.unit,
            feature.min,
            feature.max,
            feature.subFeatures,
          ),
        )
      : [],
    subFeatureSelected: null,
    reqValue: licenseType ? 0 : undefined,
  };
};
