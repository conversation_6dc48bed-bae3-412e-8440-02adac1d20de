import { get } from 'radash';
const ecfxOptValue = {
  OPTION_MINI: 1,
  OPTION_BASE: 2,
  OPTION_BASE_PLUS: 3,
};

const EC_FX_OPTIONS = [
  {
    desc: 'Mini',
    value: ecfxOptValue.OPTION_MINI,
    count: -1,
  },
  {
    desc: 'Base',
    value: ecfxOptValue.OPTION_BASE,
    count: -1,
  },
  {
    desc: 'Base + Plus',
    value: ecfxOptValue.OPTION_BASE_PLUS,
    count: -1,
  },
];

/**
 * @param accountSummary - account summary.
 * @param accountName - account name. If not provided, default to primary account
 * @param getFeatures - flag to request feature licenses
 * @param callback
 */
export const getEcLicenseOptionsForAccount = (
  accountSummary,
  accountName,
  getFeatures,
) => {
  const accounts = accountSummary;
  if (accounts) {
    const { primary, secondary } = accounts;
    let accountObj = primary ? Object.values(primary)[0] : {}; // primary as default
    if (accountName && secondary[accountName]) {
      accountObj = secondary[accountName];
    }
    return getFormattedEcLicenseOptions(accountObj, getFeatures);
  }
};

const getFormattedEcLicenseOptions = (accountSummary, getFeatures) => {
  // determine which account type it is based on enable flag
  const licType =
    get(accountSummary, 'licenses.fx.enable') &&
    accountSummary.licenses.fx.enable
      ? 'fx'
      : get(accountSummary, 'licenses.metered.enable') &&
          accountSummary.licenses.metered.enable
        ? 'metered'
        : null;
  let result = {
    fxLicenseOptions: EC_FX_OPTIONS,
    fxLicenseWithTierOptions: EC_FX_OPTIONS,
    ecFeatureLicenseOptions: {},
    ecFeatureLicenseOptionsOriginal: {},
  };
  if (licType && get(accountSummary, `licenses.${licType}`)) {
    let ecTierOptions = [];
    let ecFeatureLicenseOptions = [];

    Object.entries(accountSummary.licenses[licType]).forEach(([key, val]) => {
      if (key === 'mini' && val > 0) {
        ecTierOptions.push({
          desc: 'Mini',
          value: ecfxOptValue.OPTION_MINI,
          count: val,
        });
        return;
      }
      if (key === 'base' && val > 0) {
        ecTierOptions.push({
          desc: 'Base',
          value: ecfxOptValue.OPTION_BASE,
          count: val,
        });
        return;
      }
      if (key === 'plus' && val > 0) {
        ecTierOptions.push({
          desc: 'Base + Plus',
          value: ecfxOptValue.OPTION_BASE_PLUS,
          count: val,
        });
        return;
      }
      if (key === 'tier' && typeof val == 'object' && val != null) {
        Object.entries(val).forEach(([key, tier]) => {
          if (tier.count > 0) {
            ecTierOptions.push({
              desc: tier.display,
              value: parseInt(key),
              count: tier.count,
            });
          }
        });
      }
      if (getFeatures && key === 'feature') {
        Object.entries(val).forEach(([featureKey, featureObj]) => {
          if (featureObj.licenseType === null) {
            // parent feature
            let parentFeature = ecFeatureLicenseOptions.find(
              (item) => item.featureKey === featureKey,
            );
            if (parentFeature) {
              parentFeature.featureName = featureObj.display
                ? featureObj.display
                : featureKey;
            } else {
              ecFeatureLicenseOptions.push({
                featureKey: featureKey,
                featureName: featureObj.display
                  ? featureObj.display
                  : featureKey,
                subFeatures: [],
              });
            }
          } else {
            if (
              licType === 'metered' ||
              (licType === 'fx' && featureObj.value > 0)
            ) {
              if (featureObj.parent) {
                let parentFeature = ecFeatureLicenseOptions.find(
                  (item) => item.featureKey === featureObj.parent,
                );
                if (parentFeature) {
                  parentFeature.subFeatures.push(
                    setFeatureLicenseObj(featureKey, featureObj),
                  );
                } else {
                  // create parent feature object for subfeature
                  ecFeatureLicenseOptions.push({
                    featureKey: featureObj.parent,
                    subFeatures: [setFeatureLicenseObj(featureKey, featureObj)],
                  });
                }
              } else {
                // standalone feature
                ecFeatureLicenseOptions.push(
                  setFeatureLicenseObj(featureKey, featureObj),
                );
              }
            }
          }
        });
        result.ecFeatureLicenseOptionsOriginal = JSON.parse(
          JSON.stringify(val),
        );
        result.ecFeatureLicenseOptions = ecFeatureLicenseOptions;
      }
    });
    result.fxLicenseWithTierOptions = ecTierOptions;
  }
  return result;
};

/**
 * Get license options for all registered accounts
 * @param licenseType - fx/metered
 * @param callback
 */
export const getEcLicenseOptionsAllAccounts = (accountSummary, licenseType) => {
  const accounts = accountSummary;
  if (accounts) {
    let result = {
      fxLicenseOptions: EC_FX_OPTIONS,
      fxLicenseWithTierOptions: EC_FX_OPTIONS,
      ecFeatureLicenseOptionsOriginal: {},
      ecFeatureLicenseOptions: {},
    };
    const { primary, secondary } = accounts;
    const accountSummary = { ...primary, ...secondary };
    const ecFxTierMap = {};
    let ecFeatureLicenseOptions = [];

    accountSummary &&
      Object.entries(accountSummary).forEach(([accountName, summary]) => {
        if (get(summary, `licenses.${licenseType}`)) {
          Object.entries(summary.licenses[licenseType]).forEach(
            ([key, val]) => {
              if (key === 'mini' && val > 0 && !ecFxTierMap.mini) {
                ecFxTierMap.mini = {
                  desc: 'Mini',
                  value: ecfxOptValue.OPTION_MINI,
                };
                return;
              }
              if (key === 'base' && val > 0 && !ecFxTierMap.base) {
                ecFxTierMap.base = {
                  desc: 'Base',
                  value: ecfxOptValue.OPTION_BASE,
                };
                return;
              }
              if (key === 'plus' && val > 0 && !ecFxTierMap.plus) {
                ecFxTierMap.plus = {
                  desc: 'Base + Plus',
                  value: ecfxOptValue.OPTION_BASE_PLUS,
                };
                return;
              }

              if (key === 'tier' && typeof val == 'object' && val != null) {
                Object.entries(val).forEach(([key, tier]) => {
                  if (tier.count > 0 && !ecFxTierMap[key]) {
                    ecFxTierMap[key] = {
                      desc: tier.display,
                      value: parseInt(key),
                    };
                  }
                });
              }
              if (key === 'feature') {
                [ecFeatureLicenseOptions, val] = getEcFeatureLicenseOptions(
                  ecFeatureLicenseOptions,
                  val,
                  licenseType,
                );
                result.ecFeatureLicenseOptionsOriginal[accountName] =
                  JSON.parse(JSON.stringify(val));
              }
            },
          );
        }
      });

    result.ecFeatureLicenseOptions = ecFeatureLicenseOptions;
    result.fxLicenseWithTierOptions = Object.values(ecFxTierMap);
    return result;
  }
};

const getEcFeatureLicenseOptions = (
  ecFeatureLicenseOptions,
  feature,
  licenseType,
) => {
  Object.entries(feature).forEach(([featureKey, featureObj]) => {
    if (featureObj.licenseType === null) {
      // parent feature
      let parentFeature = (ecFeatureLicenseOptions || []).find(
        (item) => item.featureKey === featureKey,
      );
      if (parentFeature) {
        // update feature name to the parent feature name
        parentFeature.featureName = featureObj.display
          ? featureObj.display
          : featureKey;
      } else {
        ecFeatureLicenseOptions.push({
          featureKey: featureKey,
          featureName: featureObj.display ? featureObj.display : featureKey,
          subFeatures: [],
        });
      }
    } else if (
      licenseType === 'metered' ||
      (licenseType === 'fx' && featureObj.value > 0)
    ) {
      if (featureObj.parent) {
        let parentFeature = (ecFeatureLicenseOptions || []).find(
          (item) => item.featureKey === featureObj.parent,
        );
        if (parentFeature) {
          // add subfeature if not already there

          if (
            (parentFeature.subFeatures || []).find(
              (item) => item.featureKey === featureKey,
            ) === undefined
          ) {
            parentFeature.subFeatures.push(
              setFeatureLicenseObj(featureKey, featureObj),
            );
          }
        } else {
          // create parent feature object for subfeature
          ecFeatureLicenseOptions.push({
            featureKey: featureObj.parent,
            subFeatures: [setFeatureLicenseObj(featureKey, featureObj)],
          });
        }
      } else if (
        (ecFeatureLicenseOptions || []).find(
          (item) => item.featureKey === featureKey,
        ) === undefined
      ) {
        // standalone feature
        ecFeatureLicenseOptions.push(
          setFeatureLicenseObj(featureKey, featureObj),
        );
      }
    }
  });
  return [ecFeatureLicenseOptions, feature];
};

const setFeatureLicenseObj = (featureKey, featureObj) => {
  return {
    featureKey: featureKey,
    featureName: featureObj.display ? featureObj.display : featureKey,
    licenseType: featureObj.licenseType ? featureObj.licenseType : null,
    unit: featureObj.unit ? featureObj.unit : null,
    min: featureObj.min ? featureObj.min : null,
    max: featureObj.max ? featureObj.max : null,
  };
};
